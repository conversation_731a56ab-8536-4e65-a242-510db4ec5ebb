#!/bin/bash
# 部署脚本 - 将应用部署到NAS容器

# 配置
NAS_IP="************"
NAS_USER="yangpeng"
DOCKER_CMD="/share/CACHEDEV1_DATA/.qpkg/container-station/usr/bin/.libs/docker"
NAS_PATH="/share/CACHEDEV2_DATA/Container/container-station-data/application/moco-hr"
LOG_DIR="/share/CACHEDEV2_DATA/Container/container-logs"
PROJECT_DIR="$(pwd)"  # 使用本地当前目录
APP_CONTAINER_NAME="node-app"
MONGO_CONTAINER_NAME="mongo"
APP_PORT=5006

# 错误处理函数
handle_error() {
    echo "错误: $1"
    exit 1
}

echo "=== 开始部署到NAS ($NAS_IP) ==="

# 检查是否强制重新构建
FORCE_REBUILD=false
if [ "$1" = "-f" ] || [ "$1" = "--force" ]; then
    FORCE_REBUILD=true
    echo "强制重新构建前端..."
fi

# 检查前端构建
need_rebuild=$FORCE_REBUILD

if [ ! -d "frontend/build" ]; then
    echo "前端构建不存在，需要构建..."
    need_rebuild=true
else
    echo "检查前端构建是否需要更新..."

    # 检查源代码是否比构建文件更新
    if [ "frontend/src" -nt "frontend/build" ]; then
        echo "源代码已更新，需要重新构建..."
        need_rebuild=true
    elif [ "frontend/public" -nt "frontend/build" ]; then
        echo "公共文件已更新，需要重新构建..."
        need_rebuild=true
    elif [ "frontend/package.json" -nt "frontend/build" ]; then
        echo "依赖配置已更新，需要重新构建..."
        need_rebuild=true
    else
        echo "前端构建是最新的，跳过构建"
    fi
fi

if [ "$need_rebuild" = true ]; then
    echo "正在构建前端..."
    cd frontend && npm run build && cd ..
    if [ ! -d "frontend/build" ]; then
        handle_error "前端构建失败"
    fi
    echo "✅ 前端构建完成"
fi

# 准备部署文件
echo "准备部署文件..."
rm -rf deploy
mkdir -p deploy/backend
rsync -av --exclude 'node_modules' backend/ deploy/backend/ || handle_error "无法复制后端文件"
cp -r frontend/build deploy/public || handle_error "无法复制前端文件"

# 创建Dockerfile
echo "创建Dockerfile..."
cat > deploy/Dockerfile << 'EOF'
FROM node:20-bullseye
WORKDIR /app
COPY backend/package*.json ./
RUN npm install --production --no-optional
COPY backend/ ./
COPY public/ ./public/
ENV NODE_ENV=production
ENV PORT=5006
ENV HOST=0.0.0.0
ENV MONGODB_URI=mongodb://mongo:27017/hrmsdb
EXPOSE 5006
CMD ["node", "server.js"]
EOF

# 创建环境文件
echo "创建环境配置..."
cat > deploy/.env << EOF
NODE_ENV=production
MONGODB_URI=mongodb://mongo:27017/hrmsdb
PORT=3000
HOST=0.0.0.0
EOF

# 创建启动脚本
echo "创建启动脚本..."
cat > deploy/start.sh << 'EOF'
#!/bin/bash
# NAS上的启动脚本
set -e # 如果命令失败则立即退出

# 配置
APP_CONTAINER_NAME="node-app"
MONGO_CONTAINER_NAME="mongo"
NETWORK_NAME="my-network"
MONGO_VOLUME_NAME="mongo_data"
APP_PORT="5006" # NAS主机映射端口
# 使用完整的 Docker 命令路径
DOCKER_CMD="/share/CACHEDEV1_DATA/.qpkg/container-station/usr/bin/.libs/docker" 
# LOG_DIR="/share/CACHEDEV2_DATA/Container/container-station-data/application/moco-hr/logs" # 暂时禁用日志目录

# 清理旧容器... (使用 $DOCKER_CMD)
echo "清理旧容器..."
"$DOCKER_CMD" stop "$APP_CONTAINER_NAME" 2>/dev/null || true
"$DOCKER_CMD" rm "$APP_CONTAINER_NAME" 2>/dev/null || true
"$DOCKER_CMD" rmi node-app:latest 2>/dev/null || true

# 检查MongoDB容器... (使用 $DOCKER_CMD)
echo "检查MongoDB容器..."
MONGO_RUNNING=$("$DOCKER_CMD" ps -q -f name="$MONGO_CONTAINER_NAME")
if [ -z "$MONGO_RUNNING" ]; then
    echo "MongoDB容器未运行，尝试启动..."
    "$DOCKER_CMD" run -d --name "$MONGO_CONTAINER_NAME" \
        --network "$NETWORK_NAME" \
        -v "$MONGO_VOLUME_NAME:/data/db" \
        mongo:4.4
fi

# 检查Docker网络... (使用 $DOCKER_CMD)
echo "检查Docker网络..."
NETWORK_EXISTS=$("$DOCKER_CMD" network ls | grep "$NETWORK_NAME")
if [ -z "$NETWORK_EXISTS" ]; then
    echo "创建 $NETWORK_NAME 网络..."
    "$DOCKER_CMD" network create "$NETWORK_NAME"
fi

# 确保mongo容器连接到网络... (使用 $DOCKER_CMD)
echo "确保mongo容器连接到网络..."
MONGO_IN_NETWORK=$("$DOCKER_CMD" network inspect "$NETWORK_NAME" | grep "$MONGO_CONTAINER_NAME")
if [ -z "$MONGO_IN_NETWORK" ]; then
    "$DOCKER_CMD" network connect "$NETWORK_NAME" "$MONGO_CONTAINER_NAME"
fi

# 构建Docker镜像... (使用 $DOCKER_CMD)
echo "构建Docker镜像..."
"$DOCKER_CMD" build -t node-app:latest -f Dockerfile . || exit 1

# 启动应用容器... (使用 $DOCKER_CMD)
echo "启动应用容器..."
"$DOCKER_CMD" run -d \
    --name "$APP_CONTAINER_NAME" \
    --network "$NETWORK_NAME" \
    --restart unless-stopped \
    -p "$APP_PORT:5006" \
    node-app:latest

# 等待应用启动 (最多60秒)... (使用 $DOCKER_CMD)
echo "等待应用启动 (最多60秒)..."
max_retries=30
count=0
while ! curl -s "http://************:$APP_PORT/health" >/dev/null; do
    sleep 2
    count=$((count+1))
    if [ $count -ge $max_retries ]; then
        echo "应用启动超时"
        "$DOCKER_CMD" logs "$APP_CONTAINER_NAME" # 使用 $DOCKER_CMD
        exit 1
    fi
done

# 尝试动态获取NAS IP地址显示给用户
NAS_IP=$(hostname -I | awk '{print $1}' || echo "YOUR_NAS_IP") # 如果获取失败，提示用户替换

echo "✅ 部署完成！应用可能已启动 (请检查容器状态)"
echo "访问地址: http://${NAS_IP}:$APP_PORT"

exit 0
EOF
chmod +x deploy/start.sh

# 打包部署文件
echo "打包部署文件..."
tar -czf moco-hr.tar.gz -C deploy . || handle_error "打包失败"

# 上传到NAS
echo "上传到NAS..."
ssh $NAS_USER@$NAS_IP "mkdir -p $NAS_PATH" || handle_error "无法创建NAS目录"
scp moco-hr.tar.gz $NAS_USER@$NAS_IP:$NAS_PATH/ || handle_error "无法上传打包文件"

# 在NAS上执行部署
echo "在NAS上执行部署..."
ssh $NAS_USER@$NAS_IP << EOF
cd $NAS_PATH || exit 1
tar -xzf moco-hr.tar.gz || exit 1
chmod +x start.sh
./start.sh || exit 1
EOF

echo "✅ 部署完成！应用已启动"
echo "访问地址: http://$NAS_IP:$APP_PORT"