const mongoose = require('mongoose');
const emergencyContactSchema = new mongoose.Schema({
    name: String,
    phone: String,
    relationship: String
}, { _id: false });

const employeeSchema = new mongoose.Schema({
    // 个人信息
    name: { type: String, required: true },
    gender: { type: String, required: true },
    bloodType: String,
    nationality: String,    
    ethnicity: String,
    idType: String,
    idNumber: {
        type: String,
        unique: true,
        validate: {
            validator: function(v) {
                // 如果是中国籍且是居民身份证，进行严格验证
                if ((this.nationality === '中国' || this.nationality === '中华人民共和国') && 
                    this.idType === '居民身份证') {
                    return /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/.test(v);
                }
                // 其他情况只要求长度大于等于5
                return v.length >= 5;
            },
            message: props => {
                if ((props.value.nationality === '中国' || props.value.nationality === '中华人民共和国') && 
                    props.value.idType === '居民身份证') {
                    return '请输入有效的身份证号码！';
                }
                return '证件号码长度必须大于等于5位！';
            }
        }
    },
    idValidDate: { 
        type: String, 
        validate: {
            validator: function(v) {
                return v === '长期' || /^\d{4}-\d{2}-\d{2}$/.test(v);
            },
            message: '证件有效期必须是"长期"或符合日期格式(YYYY-MM-DD)'
        }
    },
    maritalStatus: String,
    birthDate: {
        type: String,
        validate: {
            validator: function(v) {
                // 验证日期格式
                if (!/^\d{4}-\d{2}-\d{2}$/.test(v)) {
                    return false;
                }
                
                // 计算年龄
                const birthDate = new Date(v);
                const today = new Date();
                let age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();
                
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }
                // 验证是否满18岁
                return age >= 18;
            },
            message: '员工必须年满18岁'
        }
    },
    birthplace: String,
    email: { type: String, match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/},
    phone: { type: String, match: /^1[3-9]\d{9}$/},
    address: String,
    medication: String,
    bankName: String,
    bankAccount: String,
    emergencyContact: emergencyContactSchema,

    // 学历信息
    firstEducation: String,
    firstEducationType: String,
    firstSchool: String,
    firstSchoolType: String,
    firstMajor: String,
    firstGraduationDate: String,
    finalEducation: String,
    finalEducationType: String,
    finalSchool: String,
    finalSchoolType: String,
    finalMajor: String,
    finalGraduationDate: String,

    // 外语信息
    languageType: String,
    languageClass: String,
    otherLanguageType: String,
    otherLanguageClass: String,
    languageListening: String,
    languageSpeaking: String,
    languageReading: String,
    languageWriting: String,

    // 工作信息
    employeeId: { type: String, unique: true, match: /^[A-Z0-9]{1,10}$/},
    workType: String, 
    status: { type: String, default: '在职' },
    entryDate: String, 
    probationMonths: String, 
    probationEndDate: String,
    firstWorkDate: String,
    position: String, 
    administrativeLevel: String,
    department: String, 
    subDepartment: { type: String, default: '',},
    supervisor: String, 
    // 新的职称级别数据结构（数组形式）
    titleLevels: { type: [String], default: [] },
    professionalQualifications: { type: [String], default: [] },
    safetyEngineerLevels: { type: [String], default: [] },
    constructorLevels: { type: [String], default: [] },
    constructorSpecialties: { type: [String], default: [] },

    // 保留旧的字段以兼容现有数据
    titleLevel: String,
    professionalQualification: String,
    safetyEngineerLevel: String,
    constructorLevel: String,
    constructorSpecialty: String,
    seniority: String
}, {
    timestamps: true
});

module.exports = mongoose.model('Employee', employeeSchema, 'employees');