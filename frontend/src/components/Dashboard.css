.dashboard-container {
    min-height: 100vh;
    background-color: #f5f7fa;
    width: 1200px;
    margin: 0 auto;
    min-width: 1200px;
}

.dashboard-main {
    padding: 40px;
    width: 100%;
    max-width: 1200px;
    min-width: 1200px;
}

.dashboard-header {
    background: white;
    padding: 20px 40px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.month-selector {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.month-selector label {
    margin-right: 8px;
    font-weight: 600;
    color: #333;
}

/* Dashboard月份选择器样式 - 与薪资管理统一 */
.dashboard-month-picker {
    border-radius: 6px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.dashboard-month-picker .ant-select-selector {
    height: 32px !important;
    padding: 0 11px !important;
    border: 2px solid #e1e8ed !important;
    border-radius: 6px !important;
    box-shadow: none !important;
    background-color: white !important;
}

.dashboard-month-picker.ant-select-focused .ant-select-selector,
.dashboard-month-picker .ant-select-selector:hover,
.dashboard-month-picker.ant-select-open .ant-select-selector {
    border-color: #2563eb !important;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2) !important;
}

/* Dashboard月份选择器下拉菜单样式 */
.dashboard-month-picker-dropdown {
    border-radius: 6px !important;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
    z-index: 1050 !important;
    max-height: 300px !important;
}

/* 隐藏Dashboard下拉菜单虚拟滚动的滚动条 */
.dashboard-month-picker-dropdown .rc-virtual-list::-webkit-scrollbar {
    display: none !important;
}

.dashboard-month-picker-dropdown .rc-virtual-list {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

.dashboard-month-picker-dropdown .rc-virtual-list-holder::-webkit-scrollbar {
    display: none !important;
}

.dashboard-month-picker-dropdown .rc-virtual-list-holder {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

.dashboard-month-picker-dropdown .ant-select-item {
    padding: 8px 12px !important;
    font-size: 14px;
}

.dashboard-month-picker-dropdown .ant-select-item-option-selected {
    background-color: #e6f7ff !important;
    font-weight: 600;
}

.dashboard-month-picker-dropdown .ant-select-item-option-active {
    background-color: #f5f5f5 !important;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-content h1 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

.page-title-text {
    color: #2c3e50;
    font-size: 28px;
    font-weight: 700;
    text-align: center;
    margin: 0;
    padding: 0;
    position: relative;
    display: inline-block;
}

.company-name {
    color: #34495e;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 2px;
}

.dashboard-nav {
    display: flex;
    gap: 15px;
}

.dashboard-nav button {
    width: 120px;
    padding: 10px 20px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0;
}

.dashboard-nav button:hover {
    background-color: #2980b9;
    transform: translateY(-1px);
}

.logout-button {
    background-color: #e74c3c !important;
}

.logout-button:hover {
    background-color: #c0392b !important;
}

.dashboard-main {
    padding: 40px;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.card {
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 为不同卡片设置不同浅色半透明颜色 */
.card:nth-child(1) {
    background-color: rgba(52, 152, 219, 0.15);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.card:nth-child(2) {
    background-color: rgba(46, 204, 113, 0.15);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.card:nth-child(3) {
    background-color: rgba(231, 76, 60, 0.15);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.card:nth-child(4) {
    background-color: rgba(155, 89, 182, 0.15);
    border: 1px solid rgba(155, 89, 182, 0.3);
}

.card h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

.card .number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card .description {
    color: #666;
    font-size: 14px;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-container {
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-height: 300px;
    transition: all 0.3s ease;
}

.chart-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 为图表容器设置不同浅色半透明颜色 */
.chart-container:nth-child(1) {
    background-color: rgba(26, 188, 156, 0.15);
    border: 1px solid rgba(26, 188, 156, 0.3);
}

.chart-container:nth-child(2) {
    background-color: rgba(243, 156, 18, 0.15);
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.chart-container h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}