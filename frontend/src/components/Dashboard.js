import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { Column } from '@antv/g2plot';
import { Select } from 'antd';
import './Dashboard.css';
import './common/DatePickerStyles.css';
import config from '../config';

// 创建一个全局事件总线，用于跨组件通信
export const dashboardEvents = {
  listeners: {},
  subscribe: (event, callback) => {
    if (!dashboardEvents.listeners[event]) {
      dashboardEvents.listeners[event] = [];
    }
    dashboardEvents.listeners[event].push(callback);
    return () => {
      dashboardEvents.listeners[event] = dashboardEvents.listeners[event].filter(cb => cb !== callback);
    };
  },
  publish: (event, data) => {
    if (dashboardEvents.listeners[event]) {
      dashboardEvents.listeners[event].forEach(callback => callback(data));
    }
  }
};

function Dashboard() {
    const location = useLocation();
    const educationChartRef = useRef(null);
    const salaryChartRef = useRef(null);

    // 获取当前年月
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    // 添加年月选择状态
    const [selectedYear, setSelectedYear] = useState(currentYear);
    const [selectedMonth, setSelectedMonth] = useState(currentMonth);
    const [availableMonths, setAvailableMonths] = useState([]);

    const [dashboardData, setDashboardData] = useState({
        employeeCount: 0,
        totalNetSalary: 0,  // 实发薪资总额
        totalGrossSalary: 0, // 应发薪资总额
        attendanceRate: 0,   // 从考勤模块获取真实数据
        educationDistribution: [],
        salaryTrend: []
    });

    // 添加一个刷新标记，用于触发数据重新加载
    const [refreshTrigger, setRefreshTrigger] = useState(0);
    // 添加状态变量，用于跟踪是否显示近6个月的数据
    const [showLast6Months, setShowLast6Months] = useState(true);
    // 数据获取函数
    const fetchDashboardData = async () => {
        try {
            console.log('获取仪表盘数据，使用API基础URL:', config.apiBaseUrl);
            console.log('当前选择的年月:', selectedYear, selectedMonth);

            // 获取员工数据（加上token）
            const employeeResponse = await fetch(`${config.apiBaseUrl}/employees`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });
            console.log('员工响应状态:', employeeResponse.status, employeeResponse.statusText);

            if (!employeeResponse.ok) {
                const errorText = await employeeResponse.text();
                throw new Error(`获取员工数据失败: ${employeeResponse.status} ${employeeResponse.statusText} - ${errorText}`);
            }

            const employees = await employeeResponse.json();
            console.log('员工数据:', employees);

            // 获取薪资数据（按选定的年月）
            console.log('请求薪资数据:', `${config.apiBaseUrl}/salary/employees?year=${selectedYear}&month=${selectedMonth}`);
            const timestamp = new Date().getTime();
            const salaryResponse = await fetch(`${config.apiBaseUrl}/salary/employees?year=${selectedYear}&month=${selectedMonth}&t=${timestamp}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            console.log('薪资响应状态:', salaryResponse.status, salaryResponse.statusText);

            if (!salaryResponse.ok) {
                const errorText = await salaryResponse.text();
                throw new Error(`获取薪资数据失败: ${salaryResponse.status} ${salaryResponse.statusText} - ${errorText}`);
            }

            const salaries = await salaryResponse.json();
            console.log('薪资数据:', salaries);

            // 如果员工数据为空但薪资数据不为空，使用薪资数据中的员工信息
            let employeeData = employees;
            if ((!employees || employees.length === 0) && salaries && salaries.length > 0) {
                console.log('员工数据为空，使用薪资数据中的员工信息');
                employeeData = salaries.map(salary => ({
                    _id: salary._id,
                    employeeId: salary.employeeId,
                    name: salary.name,
                    department: salary.department,
                    finalEducation: salary.education, // 使用薪资中的教育信息
                    finalEducationType: '全日制', // 假设为全日制
                    finalSchool: salary.school || '未知学校'
                }));
                console.log('从薪资数据构建的员工数据:', employeeData);
            }

            // 计算实发薪资总额（实际发放的薪资）
            let totalNetSalary = salaries.reduce((sum, emp) => sum + (emp.calculationResult?.netSalary || 0), 0);
            console.log('计算实发薪资总额:', totalNetSalary);

            // 计算应发薪资总额（未扣除社保、个税等前的薪资）
            let totalGrossSalary = salaries.reduce((sum, emp) => sum + (emp.calculationResult?.totalMonthlySalary || 0), 0);
            console.log('计算应发薪资总额:', totalGrossSalary);

            // 统计最高学历分布
            const educationCount = {};
            employeeData.forEach(emp => {
                let education = '';
                // 从薪资数据中获取教育信息
                if (emp.finalEducation) {
                    education = emp.finalEducation;
                } else {
                    // 查找对应的薪资记录
                    const salarySummary = salaries.find(s => s.employeeId === emp.employeeId);
                    if (salarySummary && salarySummary.education) {
                        education = salarySummary.education;
                    } else {
                        education = '大专及以下';
                    }
                }

                // 标准化学历名称
                if (education.includes('博士')) {
                    education = '博士';
                } else if (education.includes('硕士') || education.includes('研究生')) {
                    education = '硕士';
                } else if (education.includes('本科')) {
                    education = '本科';
                } else if (education.includes('大专')) {
                    education = '大专';
                } else if (education.includes('高中') || education.includes('中专')) {
                    education = '高中/中专';
                } else {
                    education = '大专及以下';
                }

                console.log('员工教育:', emp.name, education);
                if (education) {
                    educationCount[education] = (educationCount[education] || 0) + 1;
                }
            });
            console.log('学历统计:', educationCount);

            const educationOrder = {
                '博士': 1,
                '硕士': 2,
                '本科': 3,
                '大专': 4,
                '高中/中专': 5,
                '大专及以下': 6
            };
            const educationData = Object.entries(educationCount)
                .map(([type, value]) => ({ type, value }))
                .sort((a, b) => (educationOrder[a.type] || 99) - (educationOrder[b.type] || 99));
            console.log('整理后的学历数据:', educationData);

            // 构建工资趋势数据
            let salaryTrend = [];

            try {
                if (showLast6Months) {
                    // 显示近6个月的薪资数据
                    const trendPromises = [];
                    const currentDate = new Date(selectedYear, selectedMonth - 1);

                    for (let i = 5; i >= 0; i--) {
                        const date = new Date(currentDate);
                        date.setMonth(date.getMonth() - i);
                        const year = date.getFullYear();
                        const month = date.getMonth() + 1;

                        // 获取每个月的薪资统计数据
                        const statsPromise = fetch(`${config.apiBaseUrl}/salary/stats?year=${year}&month=${month}`, {
                            headers: {
                                'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                                'Content-Type': 'application/json'
                            }
                        }).then(response => {
                            if (response.ok) {
                                return response.json();
                            }
                            return { totalNetSalary: 0, year, month };
                        }).catch(() => {
                            return { totalNetSalary: 0, year, month };
                        });

                        trendPromises.push(statsPromise);
                    }

                    // 等待所有请求完成
                    const trendResults = await Promise.all(trendPromises);

                    // 构建趋势数据
                    salaryTrend = trendResults.map(result => {
                        return {
                            month: `${result.month}月`,
                            // 确保金额保留小数点后2位
                            amount: result.totalNetSalary ? Number(Number(result.totalNetSalary).toFixed(2)) : 0,
                            // 移除isDefault标记，确保所有数据都以相同方式处理
                            isDefault: false,
                            // 保存原始年月信息，用于正确排序
                            sortKey: `${result.year}${String(result.month).padStart(2, '0')}`
                        };
                    });
                } else {
                    // 只显示当前选择的月份
                    // 获取当前选择月份的薪资统计数据
                    const response = await fetch(`${config.apiBaseUrl}/salary/stats?year=${selectedYear}&month=${selectedMonth}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        salaryTrend = [{
                            month: `${selectedMonth}月`,
                            // 确保金额保留小数点后2位
                            amount: result.totalNetSalary ? Number(Number(result.totalNetSalary).toFixed(2)) : 0,
                            // 移除isDefault标记，确保所有数据都以相同方式处理
                            isDefault: false,
                            // 保存原始年月信息，用于正确排序
                            sortKey: `${selectedYear}${String(selectedMonth).padStart(2, '0')}`
                        }];
                    } else {
                        // 如果获取失败，使用当前页面上的总薪资数据
                        salaryTrend = [{
                            month: `${selectedMonth}月`,
                            amount: Number(Number(totalNetSalary).toFixed(2)),
                            isDefault: false,
                            sortKey: `${selectedYear}${String(selectedMonth).padStart(2, '0')}`
                        }];
                    }
                }

                console.log('获取到的薪资趋势数据:', salaryTrend);
            } catch (error) {
                console.error('获取薪资趋势数据失败:', error);

                if (showLast6Months) {
                    // 如果获取失败，使用默认数据（近6个月）
                    const currentDate = new Date(selectedYear, selectedMonth - 1);
                    const last6Months = [];

                    for (let i = 5; i >= 0; i--) {
                        const date = new Date(currentDate);
                        date.setMonth(date.getMonth() - i);

                        last6Months.push({
                            month: `${date.getMonth() + 1}月`,
                            // 确保金额保留小数点后2位
                            amount: i === 0 ? Number(Number(totalNetSalary).toFixed(2)) : 0,
                            // 统一设置为false，确保所有数据都以相同方式处理
                            isDefault: false,
                            // 保存原始年月信息，用于正确排序
                            sortKey: `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}`
                        });
                    }

                    salaryTrend = last6Months;
                } else {
                    // 如果获取失败，使用当前页面上的总薪资数据（单月）
                    salaryTrend = [{
                        month: `${selectedMonth}月`,
                        amount: Number(Number(totalNetSalary).toFixed(2)),
                        isDefault: false,
                        sortKey: `${selectedYear}${String(selectedMonth).padStart(2, '0')}`
                    }];
                }
            }

            // 如果是显示近6个月的数据，需要排序
            if (showLast6Months) {
                // 确保按年月排序，从左到右，最新的月份在最右侧
                salaryTrend.sort((a, b) => {
                    // 使用sortKey进行排序，sortKey格式为YYYYMM
                    return a.sortKey.localeCompare(b.sortKey);
                });
            }
            console.log('最终薪资趋势数据:', salaryTrend);

            // 获取考勤率数据 - 直接调用考勤管理的统一API
            let attendanceRate = 0;
            try {
                const attendanceResponse = await fetch(`${config.apiBaseUrl}/attendance/list?year=${selectedYear}&month=${selectedMonth}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (attendanceResponse.ok) {
                    const attendanceResult = await attendanceResponse.json();
                    if (attendanceResult.success && attendanceResult.data.employeeStats) {
                        // 计算总体出勤率，与考勤管理页面保持一致
                        const employeeStats = attendanceResult.data.employeeStats;
                        if (employeeStats.length > 0) {
                            const totalAttendanceRate = employeeStats.reduce((sum, emp) => {
                                return sum + (emp.shouldAttendDays > 0 ? (emp.actualAttendDays / emp.shouldAttendDays) : 0);
                            }, 0);
                            attendanceRate = parseFloat((totalAttendanceRate / employeeStats.length * 100).toFixed(1));
                        }
                        console.log('获取到考勤率:', attendanceRate);
                    }
                } else {
                    console.warn('获取考勤率失败，使用默认值0');
                }
            } catch (attendanceError) {
                console.warn('获取考勤率异常:', attendanceError.message);
                // 使用默认值0，不影响其他数据显示
            }

            setDashboardData({
                employeeCount: employeeData.length,
                totalNetSalary: totalNetSalary || 0,
                totalGrossSalary: totalGrossSalary || 0,
                attendanceRate: attendanceRate,
                educationDistribution: educationData,
                salaryTrend: salaryTrend
            });
            console.log('更新仪表盘数据:', {
                employeeCount: employees.length,
                totalNetSalary,
                totalGrossSalary,
                educationDistribution: educationData
            });

        } catch (error) {
            console.error('获取仪表盘数据失败:', error);
            console.error('错误详情:', error.stack);
            alert(`无法加载仪表盘数据: ${error.message}。请检查设置或联系管理员。`);
            setDashboardData(prevData => ({
                ...prevData,
                employeeCount: prevData.employeeCount || 0,
                totalNetSalary: prevData.totalNetSalary || 0,
                totalGrossSalary: prevData.totalGrossSalary || 0,
                salaryTrend: prevData.salaryTrend.length ? prevData.salaryTrend : generateDefaultSalaryTrend()
            }));
        }
    };

    // 添加生成默认薪资趋势数据的函数
    const generateDefaultSalaryTrend = () => {
        const currentDate = new Date();
        const last6Months = [];

        for (let i = 5; i >= 0; i--) {
            const date = new Date(currentDate);
            date.setMonth(date.getMonth() - i);

            last6Months.push({
                month: `${date.getMonth() + 1}月`,
                amount: 0.00, // 明确使用两位小数
                isDefault: false, // 统一设置为false，确保所有数据都以相同方式处理
                // 保存原始年月信息，用于正确排序
                sortKey: `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}`
            });
        }

        return last6Months;
    };

    // 生成可用月份列表（与其他模块保持一致）
    const generateAvailableMonths = () => {
        const months = [];
        const currentDate = new Date();

        // 生成过去36个月的月份选项（与薪资管理保持一致）
        for (let i = 0; i < 36; i++) {
            const date = new Date(currentDate);
            date.setMonth(date.getMonth() - i);

            const year = date.getFullYear();
            const month = date.getMonth() + 1;

            months.push({
                year: year,
                month: month,
                label: `${year}年${month}月`,
                value: `${year}-${month}`
            });
        }

        setAvailableMonths(months);
        console.log('生成的可用月份列表:', months.length, '个月份');
    };

    // 监听全局事件，当员工或薪资数据变化时刷新仪表盘
    useEffect(() => {
        // 订阅数据变更事件
        const unsubscribeEmployee = dashboardEvents.subscribe('employee-updated', () => {
            console.log('检测到员工数据变更，刷新仪表盘');
            // 添加短暂延迟确保后端数据已更新
            setTimeout(() => {
                setRefreshTrigger(prev => prev + 1);
            }, 500);
        });

        const unsubscribeSalary = dashboardEvents.subscribe('salary-updated', (stats) => {
            console.log('检测到薪资数据变更，更新仪表盘', stats);
            if (stats && (typeof stats.totalNetSalary === 'number' || typeof stats.totalGrossSalary === 'number')) {
                // 直接使用传递过来的统计数据
                setDashboardData(prevData => ({
                    ...prevData,
                    totalNetSalary: stats.totalNetSalary || prevData.totalNetSalary,
                    totalGrossSalary: stats.totalGrossSalary || prevData.totalGrossSalary,
                    employeeCount: stats.employeeCount || prevData.employeeCount
                }));
            } else {
                // 如果没有传递统计数据，则刷新仪表盘
                setTimeout(() => {
                    setRefreshTrigger(prev => prev + 1);
                }, 500);
            }
        });

        return () => {
            unsubscribeEmployee();
            unsubscribeSalary();
        };
    }, []);

    // 初始化时生成可用月份列表
    useEffect(() => {
        generateAvailableMonths();
    }, []);

    // 当依赖变化时获取数据
    useEffect(() => {
        fetchDashboardData();

        // 设置定时器，每60秒刷新一次数据
        const refreshInterval = setInterval(() => {
            console.log('定时刷新仪表盘数据...');
            fetchDashboardData();
        }, 60000);

        return () => {
            clearInterval(refreshInterval);
        };
    }, [location.key, refreshTrigger, selectedYear, selectedMonth, showLast6Months]); // 添加显示模式作为依赖

    // 修改人员分布图表配置
    const educationConfig = {
        data: dashboardData.educationDistribution || [],
        xField: 'type',
        yField: 'value',
        label: {
            position: 'top',
            content: (datum) => `${datum.value}人`
        },
        // 添加必要的配置
        meta: {
            type: { alias: '学历' },
            value: { alias: '人数' }
        }
    };

    // 修改工资趋势图配置
    const salaryTrendConfig = {
        data: dashboardData.salaryTrend || [],
        xField: 'month',
        yField: 'amount',
        // 添加必要的配置
        meta: {
            month: { alias: '月份' },
            amount: {
                alias: '金额',
                formatter: (value) => {
                    // 保留小数点后2位
                    return `¥${Number(value).toFixed(2)}`;
                }
            }
        },
        // 设置标签
        label: {
            // 标签位置在柱状图顶部
            position: 'top',
            // 自定义标签内容，显示金额
            content: (datum) => {
                return `¥${Number(datum.amount).toFixed(2)}`;
            },
            // 确保所有柱状图都显示标签
            style: {
                opacity: 1
            }
        },
        // X轴配置，压缩月份之间的间距
        xAxis: {
            // 减小柱状图宽度，增加间距
            tickInterval: 1,
            // 减小标签间距
            label: {
                autoRotate: false,
                autoHide: false,
                autoEllipsis: false
            }
        },
        // Y轴配置
        yAxis: {
            label: {
                // 格式化Y轴标签，保留2位小数
                formatter: (value) => {
                    // 确保值是数字并且保留2位小数，不添加货币符号
                    if (typeof value === 'number') {
                        return value.toFixed(2);
                    }
                    return value;
                }
            },
            // 确保Y轴刻度线正确显示
            nice: true,
            // 确保Y轴最小值为0
            min: 0
        },
        // 增加左侧内边距，给Y轴金额留出更多空间
        padding: [50, 20, 50, 80],
        // 减小柱状图宽度，压缩X轴空间
        columnWidthRatio: 0.5,
        // 确保图表能够自适应容器大小
        autoFit: true
    };

    useEffect(() => {
        // 初始化教育分布图表
        if (educationChartRef.current && dashboardData.educationDistribution.length > 0) {
            const chart = new Column(educationChartRef.current, educationConfig);
            chart.render();
            return () => chart.destroy();
        }
    }, [dashboardData.educationDistribution]);

    useEffect(() => {
        // 初始化工资趋势图表
        if (salaryChartRef.current && dashboardData.salaryTrend.length > 0) {
            const chart = new Column(salaryChartRef.current, salaryTrendConfig);
            chart.render();
            return () => chart.destroy();
        }
    }, [dashboardData.salaryTrend]);

    // 处理月份选择变化
    const handleMonthChange = (value) => {
        // 检查值是否有效
        if (!value || value === 'undefined-undefined' || !value.includes('-')) {
            console.error('无效的月份选择值:', value);
            // 使用当前年月作为默认值
            const now = new Date();
            const currentYear = now.getFullYear();
            const currentMonth = now.getMonth() + 1;
            setSelectedYear(currentYear);
            setSelectedMonth(currentMonth);
            setShowLast6Months(true);
            return;
        }

        // 解析年月并确保它们是有效的数字
        const parts = value.split('-');
        const year = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10);

        // 验证年月是否有效
        if (isNaN(year) || isNaN(month) || year < 2000 || year > 2100 || month < 1 || month > 12) {
            console.error('无效的年月值:', year, month);
            // 使用当前年月作为默认值
            const now = new Date();
            const currentYear = now.getFullYear();
            const currentMonth = now.getMonth() + 1;
            setSelectedYear(currentYear);
            setSelectedMonth(currentMonth);
            setShowLast6Months(true);
            return;
        }

        // 设置有效的年月
        setSelectedYear(year);
        setSelectedMonth(month);

        // 如果选择的是当前月份，显示近6个月的数据
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;

        if (year === currentYear && month === currentMonth) {
            // 当前月份，显示近6个月的数据
            setShowLast6Months(true);
        } else {
            // 非当前月份，只显示选择的月份
            setShowLast6Months(false);
        }
    };

    return (
        <div className="dashboard-container">
            <header className="dashboard-header">
                <div className="page-title-text">首页 - 信息统计</div>
                <div className="month-selector">
                    <label>选择月份：</label>
                    <Select
                        placeholder="选择月份"
                        value={`${selectedYear}-${selectedMonth}`}
                        onChange={(value) => handleMonthChange(value)}
                        className="dashboard-month-picker"
                        popupClassName="dashboard-month-picker-dropdown"
                        style={{ width: 150 }}
                    >
                        {availableMonths.map(month => {
                            // 确保年月都是有效的数字
                            if (typeof month.year !== 'number' || isNaN(month.year) ||
                                typeof month.month !== 'number' || isNaN(month.month)) {
                                return null; // 跳过无效的月份
                            }

                            return (
                                <Select.Option
                                    key={`${month.year}-${month.month}`}
                                    value={`${month.year}-${month.month}`}
                                >
                                    {month.label}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </div>
            </header>
            <main className="dashboard-main">
                <div className="dashboard-cards">
                    <div className="card">
                        <h3>员工总数</h3>
                        <p className="number">{dashboardData.employeeCount}</p>
                    </div>
                    <div className="card">
                        <h3>应发薪资总额</h3>
                        <p className="number">¥{Number(dashboardData.totalGrossSalary).toFixed(2)}</p>
                    </div>
                    <div className="card">
                        <h3>实发薪资总额</h3>
                        <p className="number">¥{Number(dashboardData.totalNetSalary).toFixed(2)}</p>
                    </div>
                    <div className="card">
                        <h3>考勤率</h3>
                        <p className="number">{dashboardData.attendanceRate}%</p>
                    </div>
                </div>
                <div className="dashboard-charts">
                    <div className="chart-container">
                        <h3>人员分布</h3>
                        <div ref={educationChartRef} style={{ height: '300px' }}></div>
                    </div>
                    <div className="chart-container">
                        <h3>工资趋势{showLast6Months ? '（近6个月）' : `（${selectedYear}年${selectedMonth}月）`}</h3>
                        <div ref={salaryChartRef} style={{ height: '350px' }}></div>
                    </div>
                </div>
            </main>
        </div>
    );
}

export default Dashboard;