.attendance-management-container {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 0;
}

.header-content {
    align-items: center;
    margin-top: 20px;
    padding: 0 20px;
    margin-bottom: 0;
}

.header-content h2 {
    color: #2c3e50;
    font-size: 24px;
    margin: 0;
}

/* 新的操作和过滤条样式 */
.action-filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 0 10px;
}

.filter-area {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-group, .month-group {
    display: flex;
    align-items: center;
}

.search-group label, .month-group label {
    margin-right: 8px;
    font-weight: 500;
    color: #333;
}

/* 月份选择器样式 */
.month-picker {
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 修复日期选择器下拉菜单样式 */
.month-picker-dropdown {
    border-radius: 6px !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
    z-index: 1050 !important;
}

.ant-picker-dropdown {
    z-index: 1050 !important;
}

.ant-picker-panel-container {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
}

.action-buttons-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* ====================== 按钮样式分组 ====================== */

/* 按钮基础样式 */
.back-button,
.upload-button,
.refresh-button {
    display: inline-block;
    text-align: center;
    height: 32px;
    line-height: 32px;
    padding: 0 15px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    white-space: nowrap;
    min-width: 90px;
    width: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 返回按钮 - 蓝色 */
.back-button {
    background-color: #3b82f6;
}

.back-button:hover {
    background-color: #2563eb;
}

/* 上传按钮 - 绿色 */
.upload-button {
    background-color: #10b981;
}

.upload-button:hover {
    background-color: #059669;
}

.upload-button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}



/* 刷新按钮 - 青色 */
.refresh-button {
    background-color: #06b6d4;
}

.refresh-button:hover {
    background-color: #0891b2;
}

.refresh-button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}

/* 按钮图标 */
.button-icon {
    font-size: 14px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
}

/* 页面按钮 */
.page-button {
    display: inline-block;
    text-align: center;
    height: 30px;
    line-height: 30px;
    padding: 0 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-button:hover {
    border-color: #1890ff;
    color: #1890ff;
}

/* 考勤配置信息卡片 */
.config-info-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
}

.config-info-card h3 {
    color: #1890ff;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
}

.config-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.config-item strong {
    color: #333;
    margin-right: 8px;
    min-width: 80px;
}

.config-item span {
    color: #666;
}

/* 统计卡片样式 */
.statistics-cards {
    margin: 25px 20px;
    width: calc(100% - 40px);
}

.statistics-cards .ant-row {
    margin: 0 !important;
    width: 100% !important;
}

.statistics-cards .ant-col {
    padding: 0 !important;
    margin-bottom: 8px;
}

.ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    height: 100%;
}

.statistics-cards .ant-card {
    height: 88px;
}

.statistics-cards .ant-card-body {
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.ant-card-head {
    border-bottom: 1px solid #f0f0f0;
}

.ant-card-body {
    padding: 20px;
}

.ant-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #d9d9d9;
}

/* 统计标题和数值样式 */
.ant-statistic-title {
    color: #8c8c8c;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.2;
}

.ant-statistic-content {
    color: #262626;
    font-size: 20px;
    font-weight: 600;
    line-height: 1;
}

.ant-statistic-content-value {
    color: inherit;
    font-size: inherit;
    font-weight: inherit;
}

.statistics-cards .ant-statistic-title {
    color: #666;
    font-size: 11px;
}

.statistics-cards .ant-statistic-content-value {
    font-size: 18px;
}

/* 表格容器 */
.table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
    margin: 20px 20px;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
}

th, td {
    padding: 12px 8px;
    text-align: center;
    border-right: 1px solid #f0f0f0;
    font-size: 13px;
}

th {
    background-color: #fafafa;
    font-weight: 600;
    color: #262626;
    border-bottom: 2px solid #f0f0f0;
}

td {
    color: #595959;
    border-bottom: 1px solid #f0f0f0;
}

tr:hover {
    background-color: #f5f5f5;
}

/* 分页样式 */
.ant-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    padding: 0;
}

.page-button.page-info {
    border: none;
    background: none;
    color: #666;
    cursor: default;
    padding: 0 8px;
    font-size: 13px;
}

/* 搜索输入框样式 */
.attendance-search-input {
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.attendance-search-input .ant-input {
    border-radius: 6px;
    font-size: 13px;
    height: 32px;
}

.attendance-search-input .ant-input-search-button {
    border-radius: 0 6px 6px 0;
    height: 32px;
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
    .header-content {
        padding: 0 15px;
    }
    
    .action-filter-bar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .filter-area, .action-buttons-group {
        justify-content: center;
        flex-wrap: wrap;
    }

    .config-info-card,
    .statistics-cards,
    .table-container {
        margin-left: 15px;
        margin-right: 15px;
    }

    .statistics-cards {
        width: calc(100% - 30px);
    }

    .ant-card {
        margin-bottom: 12px;
    }

    th, td {
        font-size: 12px;
        padding: 8px 6px;
    }
}

@media screen and (max-width: 992px) {
    .action-filter-bar {
        padding: 0 5px;
    }

    .filter-area {
        flex-direction: column;
        gap: 10px;
    }

    .action-buttons-group {
        flex-wrap: wrap;
        gap: 8px;
    }

    .config-info-card,
    .statistics-cards,
    .table-container {
        margin-left: 10px;
        margin-right: 10px;
    }

    .statistics-cards {
        width: calc(100% - 20px);
    }

    .ant-card {
        margin-bottom: 10px;
    }

    .ant-statistic-title {
        font-size: 11px;
    }

    .ant-statistic-content-value {
        font-size: 16px;
    }
}

@media screen and (max-width: 768px) {
    .header-content h2 {
        font-size: 20px;
    }

    .config-info-card,
    .statistics-cards,
    .table-container {
        margin-left: 5px;
        margin-right: 5px;
    }

    .statistics-cards {
        width: calc(100% - 10px);
    }

    .config-info-card {
        padding: 15px;
    }

    .config-item {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 12px;
    }

    .config-item strong {
        margin-bottom: 4px;
        min-width: auto;
    }

    .ant-card {
        margin-bottom: 8px;
    }

    .ant-statistic-title {
        font-size: 10px;
    }

    .ant-statistic-content-value {
        font-size: 14px;
    }

    th, td {
        font-size: 11px;
        padding: 6px 4px;
    }
} 