import React, { useState, useEffect, useRef } from 'react';
import { Table, Card, Upload, Button, message, Spin, DatePicker, Select, Row, Col, Statistic, Input } from 'antd';
import { UploadOutlined, CalendarOutlined, ReloadOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import './AttendanceManagement.css';
import '../common/TextLinkStyles.css';
import '../common/DatePickerStyles.css';

const { Option } = Select;

const AttendanceManagement = () => {
    const navigate = useNavigate();
    const [uploading, setUploading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [attendanceStats, setAttendanceStats] = useState([]);
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
    const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
    const [summaryData, setSummaryData] = useState(null);
    const [attendanceConfig, setAttendanceConfig] = useState(null);
    const [searchText, setSearchText] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const messageShownRef = useRef(new Set()); // 跟踪已显示过消息的月份

    // 考勤统计表格列定义
    const statsColumns = [
        { 
            title: '工号', 
            dataIndex: 'employeeId', 
            key: 'employeeId',
            width: 80,
            align: 'center',
            fixed: 'left'
        },
        { 
            title: '姓名', 
            dataIndex: 'name', 
            key: 'name',
            width: 100,
            align: 'center',
            fixed: 'left'
        },
        { 
            title: '应出勤天数', 
            dataIndex: 'shouldAttendDays', 
            key: 'shouldAttendDays',
            width: 100,
            align: 'center'
        },
        { 
            title: '实际出勤天数', 
            dataIndex: 'actualAttendDays', 
            key: 'actualAttendDays',
            width: 100,
            align: 'center',
            render: (text, record) => (
                <span style={{ color: text === record.shouldAttendDays ? '#52c41a' : '#fa8c16' }}>
                    {text}
                </span>
            )
        },
        { 
            title: '缺勤天数', 
            dataIndex: 'absentDays', 
            key: 'absentDays',
            width: 80,
            align: 'center',
            render: (text) => (
                <span style={{ color: text > 0 ? '#ff4d4f' : '#52c41a' }}>
                    {text}
                </span>
            )
        },
        { 
            title: '迟到天数', 
            dataIndex: 'lateDays', 
            key: 'lateDays',
            width: 80,
            align: 'center',
            render: (text) => (
                <span style={{ color: text > 0 ? '#fa8c16' : '#52c41a' }}>
                    {text}
                </span>
            )
        },
        { 
            title: '早退天数', 
            dataIndex: 'earlyDays', 
            key: 'earlyDays',
            width: 80,
            align: 'center',
            render: (text) => (
                <span style={{ color: text > 0 ? '#fa8c16' : '#52c41a' }}>
                    {text}
                </span>
            )
        },
        { 
            title: '总工时', 
            dataIndex: 'totalWorkHours', 
            key: 'totalWorkHours',
            width: 90,
            align: 'center',
            render: (text) => (
                <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                    {(text || 0).toFixed(1)}h
                </span>
            )
        },
        { 
            title: '加班时长', 
            dataIndex: 'totalOvertimeHours', 
            key: 'totalOvertimeHours',
            width: 90,
            align: 'center',
            render: (text) => (
                <span style={{ 
                    color: text > 0 ? '#722ed1' : '#666',
                    fontWeight: text > 0 ? 'bold' : 'normal'
                }}>
                    {(text || 0).toFixed(1)}h
                </span>
            )
        },
        {
            title: '出勤率',
            key: 'attendanceRate',
            width: 80,
            align: 'center',
            render: (text, record) => {
                const rate = record.shouldAttendDays > 0 
                    ? ((record.actualAttendDays / record.shouldAttendDays) * 100).toFixed(1)
                    : '0.0';
                return (
                    <span style={{ 
                        color: parseFloat(rate) >= 95 ? '#52c41a' : 
                               parseFloat(rate) >= 80 ? '#fa8c16' : '#ff4d4f',
                        fontWeight: 'bold'
                    }}>
                        {rate}%
                    </span>
                );
            }
        }
    ];



    // 加载考勤配置
    const loadAttendanceConfig = async () => {
        try {
            const response = await fetch('/api/attendance-config/default');
            
            if (!response.ok) {
                console.log('考勤配置API不存在或返回错误，跳过加载配置');
                return;
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const result = await response.json();
                if (result.success) {
                    setAttendanceConfig(result.data);
                }
            }
        } catch (error) {
            console.error('加载考勤配置失败:', error);
            // 考勤配置不是必需的，所以不显示错误消息
        }
    };

    const loadAttendanceStats = async () => {
        setLoading(true);
        try {
            console.log(`正在加载 ${selectedYear}年${selectedMonth}月 的考勤统计数据...`);
            
            const response = await fetch(`/api/attendance/list?year=${selectedYear}&month=${selectedMonth}`);
            
            console.log('API响应状态:', response.status);
            console.log('API响应头:', response.headers.get('content-type'));
            
            // 检查响应状态
            if (!response.ok) {
                const errorText = await response.text();
                console.error('API错误响应:', errorText);
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 检查内容类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const responseText = await response.text();
                console.error('非JSON响应内容:', responseText);
                throw new Error('服务器返回的不是JSON格式的数据');
            }
            
            const result = await response.json();
            console.log('考勤统计接口响应:', result);
            
            if (result.success) {
                // 处理后端返回的数据结构
                if (result.data && result.data.employeeStats) {
                    // 设置考勤统计数据并按工号排序（完全参考员工列表的排序逻辑）
                    const sortedStats = (result.data.employeeStats || []).sort((a, b) => {
                        const idA = a.employeeId || '';
                        const idB = b.employeeId || '';

                        // 如果工号为空，则放到最后
                        if (!idA) return 1;
                        if (!idB) return -1;

                        // 先按首字母排序
                        const letterA = idA.charAt(0).toUpperCase();
                        const letterB = idB.charAt(0).toUpperCase();

                        if (letterA !== letterB) {
                            return letterA.localeCompare(letterB);
                        }

                        // 如果首字母相同，则按数字部分排序
                        const numA = parseInt(idA.substring(1)) || 0;
                        const numB = parseInt(idB.substring(1)) || 0;

                        return numA - numB;
                    });
                    setAttendanceStats(sortedStats);
                    console.log('考勤统计数据:', sortedStats);
                    
                    // 设置汇总数据
                    const summaryData = {
                        year: result.data.year,
                        month: result.data.month,
                        workDays: result.data.workDays,
                        totalRecords: result.data.totalRecords,
                        config: result.data.config
                    };
                    setSummaryData(summaryData);
                    console.log('汇总数据:', summaryData);
                    
                    // 避免重复显示成功消息
                    const monthKey = `${selectedYear}-${selectedMonth}`;
                    if (!messageShownRef.current.has(monthKey)) {
                        messageShownRef.current.add(monthKey);
                        message.success(`成功加载${selectedYear}年${selectedMonth}月考勤数据`);
                    }
                } else {
                    // 如果返回的是原始记录列表，设置为空统计
                    setAttendanceStats([]);
                    setSummaryData(null);
                    message.info('没有找到该月份的考勤统计数据');
                }
            } else {
                message.error(result.message || '加载考勤统计失败');
                setAttendanceStats([]);
                setSummaryData(null);
            }
        } catch (error) {
            console.error('加载考勤统计失败:', error);
            if (error.message.includes('JSON')) {
                message.error('服务器响应格式错误，请检查API服务是否正常运行');
            } else {
                message.error('加载考勤统计失败: ' + error.message);
            }
            setAttendanceStats([]);
            setSummaryData(null);
        } finally {
            setLoading(false);
        }
    };

    const handleUpload = async (file) => {
        const formData = new FormData();
        formData.append('attendance', file);
        
        setUploading(true);
        try {
            const response = await fetch('/api/attendance/upload', {
                method: 'POST',
                body: formData,
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('上传成功，返回数据:', result);
                const stats = result.stats || {};
                console.log('统计数据:', stats);
                const messageText = `成功导入考勤数据！总计: ${stats.total || 0} 条，成功: ${stats.success || 0} 条，跳过: ${stats.skipped || 0} 条，保存: ${stats.saved || 0} 条`;
                message.success(messageText);
                // 重新加载考勤数据
                loadAttendanceStats();
            } else {
                message.error(result.message || '上传失败');
            }
        } catch (error) {
            message.error('上传失败: ' + error.message);
        } finally {
            setUploading(false);
        }
        
        return false; // 阻止默认上传行为
    };



    // 年份和月份选项
    const yearOptions = Array.from({ length: 5 }, (_, i) => {
        const year = new Date().getFullYear() - i;
        return <Option key={year} value={year}>{year}年</Option>;
    });

    const monthOptions = Array.from({ length: 12 }, (_, i) => (
        <Option key={i + 1} value={i + 1}>{i + 1}月</Option>
    ));

    // 文件上传配置
    const uploadProps = {
        name: 'attendance',
        multiple: false,
        accept: '.xls,.xlsx,.csv',
        beforeUpload: handleUpload,
        showUploadList: false,
        disabled: uploading
    };



    useEffect(() => {
        loadAttendanceConfig();
        loadAttendanceStats();
    }, [selectedYear, selectedMonth]);

    // 计算总体统计数据
    const totalStats = {
        totalEmployees: attendanceStats.length,
        totalAbsent: attendanceStats.reduce((sum, emp) => sum + (emp.absentDays || 0), 0),
        totalLate: attendanceStats.reduce((sum, emp) => sum + (emp.lateDays || 0), 0),
        totalEarly: attendanceStats.reduce((sum, emp) => sum + (emp.earlyDays || 0), 0),
        totalOvertime: attendanceStats.reduce((sum, emp) => sum + (emp.totalOvertimeHours || 0), 0)
    };

    // 计算总体出勤率
    const overallAttendanceRate = attendanceStats.length > 0 
        ? (attendanceStats.reduce((sum, emp) => {
            return sum + (emp.shouldAttendDays > 0 ? (emp.actualAttendDays / emp.shouldAttendDays) : 0);
          }, 0) / attendanceStats.length * 100).toFixed(1)
        : '0.0';

    // 过滤和排序数据（完全参考员工列表的排序逻辑）
    const filteredData = attendanceStats
        .filter(item => {
            const nameMatch = !searchText || item.name.toLowerCase().includes(searchText.toLowerCase()) ||
                             item.employeeId.toString().includes(searchText);
            return nameMatch;
        })
        .sort((a, b) => {
            const idA = a.employeeId || '';
            const idB = b.employeeId || '';

            // 如果工号为空，则放到最后
            if (!idA) return 1;
            if (!idB) return -1;

            // 先按首字母排序
            const letterA = idA.charAt(0).toUpperCase();
            const letterB = idB.charAt(0).toUpperCase();

            if (letterA !== letterB) {
                return letterA.localeCompare(letterB);
            }

            // 如果首字母相同，则按数字部分排序
            const numA = parseInt(idA.substring(1)) || 0;
            const numB = parseInt(idB.substring(1)) || 0;

            return numA - numB;
        });

    // 处理表格变化
    const handleTableChange = (pagination, filters, sorter) => {
        setCurrentPage(pagination.current);
    };



    return (
        <div className="attendance-management-container">
            <div className="header-content">
                <div className="action-filter-bar">
                    <div className="filter-area">
                        <div className="search-group">
                            <label>搜索:</label>
                            <Input.Search
                                placeholder="输入姓名或工号搜索"
                                value={searchText}
                                onChange={(e) => setSearchText(e.target.value)}
                                style={{ width: 200 }}
                                className="attendance-search-input"
                            />
                        </div>
                        <div className="month-picker-container">
                            <label>考勤月份：</label>
                            <Select
                                value={`${selectedYear}-${selectedMonth}`}
                                onChange={(value) => {
                                    const [year, month] = value.split('-');
                                    setSelectedYear(parseInt(year));
                                    setSelectedMonth(parseInt(month));
                                }}
                                className="month-picker"
                                popupClassName="month-picker-dropdown"
                            >
                                {Array.from({ length: 36 }).map((_, index) => {
                                    const date = dayjs().subtract(index, 'months');
                                    const year = date.year();
                                    const month = date.month() + 1;
                                    return (
                                        <Select.Option key={`${year}-${month}`} value={`${year}-${month}`}>
                                            {`${year}年${month}月`}
                                        </Select.Option>
                                    );
                                })}
                            </Select>
                        </div>
                    </div>
                    <div className="action-buttons-group">
                        <button className="action-button back-button" onClick={() => navigate('/dashboard')}>
                            <i className="button-icon">←</i>
                            返回
                        </button>
                        <Upload {...uploadProps}>
                            <button className="action-button upload-button" disabled={uploading}>
                                <UploadOutlined className="button-icon" />
                                {uploading ? '上传中...' : '上传考勤'}
                            </button>
                        </Upload>
                        <button className="action-button refresh-button" onClick={() => {
                            const monthKey = `${selectedYear}-${selectedMonth}`;
                            messageShownRef.current.delete(monthKey); // 清除消息记录，允许重新显示
                            loadAttendanceStats();
                        }} disabled={loading}>
                            <ReloadOutlined className="button-icon" />
                            刷新数据
                        </button>
                    </div>
                </div>
            </div>

            {/* 考勤配置信息 */}
            {(attendanceConfig || summaryData?.config) && (
                <div className="config-info-card">
                    <h3>当前考勤配置：{attendanceConfig?.name || summaryData?.config?.name || '默认配置'}</h3>
                    <Row gutter={16}>
                        <Col span={6}>
                            <div className="config-item">
                                <strong>工作日：</strong>
                                <span>
                                    {attendanceConfig?.workDays ? 
                                        attendanceConfig.workDays.map(day => {
                                            const dayLabels = ['日', '一', '二', '三', '四', '五', '六'];
                                            return `周${dayLabels[day]}`;
                                        }).join(', ') :
                                        summaryData?.config?.workDaysLabels || '周一到周五'
                                    }
                                </span>
                            </div>
                        </Col>
                        <Col span={6}>
                            <div className="config-item">
                                <strong>工作时间：</strong>
                                <span>
                                    {(attendanceConfig?.standardWorkTime || summaryData?.config?.standardWorkTime) ?
                                        `${(attendanceConfig?.standardWorkTime || summaryData?.config?.standardWorkTime).startTime} - ${(attendanceConfig?.standardWorkTime || summaryData?.config?.standardWorkTime).endTime}` :
                                        '09:00 - 18:00'
                                    }
                                </span>
                            </div>
                        </Col>
                        <Col span={6}>
                            <div className="config-item">
                                <strong>迟到容忍：</strong>
                                <span>
                                    {attendanceConfig?.lateEarlyRules?.lateTolerance || '10'}分钟
                                </span>
                            </div>
                        </Col>
                        <Col span={6}>
                            <div className="config-item">
                                <strong>午休时间：</strong>
                                <span>
                                    {attendanceConfig?.lunchBreak?.enabled ? 
                                        `${attendanceConfig.lunchBreak.startTime}-${attendanceConfig.lunchBreak.endTime}` : 
                                        '12:00-13:00'
                                    }
                                </span>
                            </div>
                        </Col>
                    </Row>
                </div>
            )}

            {/* 统计卡片 */}
            <div className="statistics-cards">
                <Row gutter={[8, 8]} style={{ margin: '0 auto', width: '100%' }}>
                    {[
                        { title: "总员工数", value: totalStats.totalEmployees, suffix: "人", precision: 0 },
                        { title: "工作日天数", value: summaryData?.workDays || 22, suffix: "天", precision: 0 },
                        { title: "总体出勤率", value: parseFloat(overallAttendanceRate), suffix: "%", precision: 1, 
                          valueStyle: { 
                              color: parseFloat(overallAttendanceRate) >= 95 ? '#3f8600' : 
                                     parseFloat(overallAttendanceRate) >= 80 ? '#fa8c16' : '#cf1322'
                          }
                        },
                        { title: "总缺勤天数", value: totalStats.totalAbsent, suffix: "天", precision: 0,
                          valueStyle: { color: totalStats.totalAbsent > 0 ? '#cf1322' : '#3f8600' }
                        },
                        { title: "总迟到天数", value: totalStats.totalLate, suffix: "天", precision: 0,
                          valueStyle: { color: totalStats.totalLate > 0 ? '#fa8c16' : '#3f8600' }
                        },
                        { title: "总加班时长", value: totalStats.totalOvertime, suffix: "小时", precision: 1,
                          valueStyle: { color: totalStats.totalOvertime > 0 ? '#1890ff' : '#666' }
                        }
                    ].map(item => (
                        <Col xs={12} sm={8} md={6} lg={4} key={item.title} style={{ display: 'flex' }}>
                            <Card style={{ width: '100%', flex: 1, margin: '0 2px' }}>
                                <Statistic
                                    title={item.title}
                                    value={item.value}
                                    precision={item.precision}
                                    suffix={item.suffix}
                                    valueStyle={item.valueStyle}
                                />
                            </Card>
                        </Col>
                    ))}
                </Row>
            </div>

            {/* 表格容器 */}
            <div className="table-container">
                <Table
                    columns={statsColumns}
                    dataSource={filteredData}
                    loading={loading}
                    rowKey="employeeId"
                    scroll={{ x: 'max-content' }}
                    bordered
                    size="small"
                    tableLayout="fixed"
                    onChange={handleTableChange}
                    pagination={{
                        position: ['bottomCenter'],
                        showSizeChanger: true,
                        showQuickJumper: true,
                        style: { padding: '16px 0' },
                        total: filteredData.length,
                        pageSize: 20,
                        current: currentPage,
                        showTotal: (total) => (
                            <span>
                                第 <Input
                                    style={{ width: '45px', margin: '0 8px', textAlign: 'center' }}
                                    onPressEnter={(e) => {
                                        const page = parseInt(e.target.value);
                                        if (page && page > 0 && page <= Math.ceil(total / 20)) {
                                            e.target.blur();
                                            setCurrentPage(page);
                                        }
                                    }}
                                /> 页/共 {Math.ceil(total / 20)} 页
                            </span>
                        ),
                        itemRender: (_, type, originalElement) => {
                            if (type === 'prev') {
                                return <button className="page-button">上一页</button>;
                            }
                            if (type === 'next') {
                                return <button className="page-button">下一页</button>;
                            }
                            return originalElement;
                        }
                    }}
                />
            </div>
        </div>
    );
};

export default AttendanceManagement;