/* 统一的日期选择器样式 */

/* 月份选择器通用样式 */
.month-picker-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.month-picker-container label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

/* Ant Design Select 样式统一 */
.month-picker.ant-select {
    width: 140px !important;
    border-radius: 6px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;
}

.month-picker.ant-select:hover {
    border-color: #40a9ff !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2) !important;
}

.month-picker.ant-select-focused {
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.month-picker .ant-select-selector {
    padding: 6px 12px !important;
    height: 36px !important;
    border-radius: 6px !important;
    border: 1px solid #d9d9d9 !important;
    background: #fff !important;
    transition: all 0.3s ease !important;
}

.month-picker .ant-select-selection-item {
    font-size: 14px !important;
    color: #333 !important;
    line-height: 22px !important;
}

.month-picker .ant-select-arrow {
    color: #999 !important;
    transition: all 0.3s ease !important;
}

.month-picker.ant-select-open .ant-select-arrow {
    color: #1890ff !important;
    transform: rotate(180deg) !important;
}

/* 下拉菜单样式 */
.month-picker-dropdown.ant-select-dropdown {
    border-radius: 8px !important;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #f0f0f0 !important;
    max-height: 300px !important;
    overflow-y: auto !important;
}

.month-picker-dropdown .ant-select-item {
    padding: 8px 12px !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
}

.month-picker-dropdown .ant-select-item:hover {
    background-color: #f5f7fa !important;
}

.month-picker-dropdown .ant-select-item-option-selected {
    background-color: #e6f7ff !important;
    color: #1890ff !important;
    font-weight: 500 !important;
}

.month-picker-dropdown .ant-select-item-option-active {
    background-color: #f0f8ff !important;
}

/* 原生select样式（用于Dashboard） */
.native-month-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.native-month-selector label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

.native-month-selector select {
    padding: 6px 12px !important;
    border-radius: 6px !important;
    border: 1px solid #d9d9d9 !important;
    background: #fff !important;
    font-size: 14px !important;
    color: #333 !important;
    width: 140px !important;
    height: 36px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.native-month-selector select:hover {
    border-color: #40a9ff !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2) !important;
}

.native-month-selector select:focus {
    outline: none !important;
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 修复滚动条样式 */
.month-picker-dropdown::-webkit-scrollbar {
    width: 6px;
}

.month-picker-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.month-picker-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    transition: background 0.3s ease;
}

.month-picker-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 通用滚动条修复 */
.ant-select-dropdown::-webkit-scrollbar {
    width: 6px !important;
}

.ant-select-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 3px !important;
}

.ant-select-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 3px !important;
    transition: background 0.3s ease !important;
}

.ant-select-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

/* 修复双滚动条问题 */
.ant-select-dropdown .rc-virtual-list {
    max-height: 280px !important;
    overflow-y: hidden !important; /* 隐藏内层滚动条 */
}

.ant-select-dropdown .rc-virtual-list-holder {
    overflow-y: hidden !important; /* 隐藏内层滚动条 */
    overflow-anchor: none !important;
}

/* 确保外层容器有滚动条 */
.ant-select-dropdown {
    overflow-y: auto !important;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
    .month-picker.ant-select,
    .native-month-selector select {
        width: 120px !important;
        font-size: 13px !important;
    }
    
    .month-picker-container,
    .native-month-selector {
        gap: 6px;
    }
    
    .month-picker-container label,
    .native-month-selector label {
        font-size: 13px;
    }
}

@media screen and (max-width: 480px) {
    .month-picker.ant-select,
    .native-month-selector select {
        width: 100px !important;
        font-size: 12px !important;
    }

    .month-picker-container label,
    .native-month-selector label {
        font-size: 12px;
    }
}

/* 修复Ant Design Select下拉菜单双滚动条问题 */
.ant-select-dropdown {
    max-height: 300px !important;
}

/* 隐藏虚拟滚动容器的滚动条，保留功能 */
.ant-select-dropdown .rc-virtual-list::-webkit-scrollbar {
    display: none !important;
}

.ant-select-dropdown .rc-virtual-list {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

/* 隐藏虚拟滚动holder的滚动条 */
.ant-select-dropdown .rc-virtual-list-holder::-webkit-scrollbar {
    display: none !important;
}

.ant-select-dropdown .rc-virtual-list-holder {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

/* 确保下拉菜单项的样式一致 */
.ant-select-dropdown .ant-select-item {
    padding: 8px 12px !important;
    font-size: 14px !important;
    transition: background-color 0.2s ease !important;
}

.ant-select-dropdown .ant-select-item-option-selected {
    background-color: #e6f7ff !important;
    font-weight: 600 !important;
}

.ant-select-dropdown .ant-select-item-option-active {
    background-color: #f5f5f5 !important;
}

/* 特殊处理部门选择器和月份选择器的下拉菜单 */
.salary-department-select + .ant-select-dropdown,
.dashboard-month-picker-dropdown,
.month-picker-dropdown {
    border-radius: 6px !important;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
    z-index: 1050 !important;
}

/* 自定义下拉菜单滚动条样式 */
.ant-select-dropdown::-webkit-scrollbar {
    width: 6px !important;
}

.ant-select-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 3px !important;
}

.ant-select-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 3px !important;
}

.ant-select-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

.ant-select-dropdown {
    scrollbar-width: thin !important; /* Firefox */
    scrollbar-color: #c1c1c1 #f1f1f1 !important; /* Firefox */
}
