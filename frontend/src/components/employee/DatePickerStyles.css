/* 自定义日期选择器样式 */
.custom-popper-container {
    position: absolute;
    z-index: 1000;
}

/* 确保日期选择器弹出层不会被截断 */
.react-datepicker-popper {
    z-index: 1000 !important;
    margin-top: 50px !important;  /* 向下移动弹出框，确保不遮挡输入框 */
}

/* 调整日期选择器弹出层的位置 */
.react-datepicker-popper[data-placement^="bottom"] {
    margin-top: 50px !important;  /* 向下移动弹出框，确保不遮挡输入框 */
}

.react-datepicker-popper[data-placement^="top"] {
    margin-bottom: 50px !important;  /* 确保顶部弹出时有足够的间距 */
}

/* 确保日期选择器内的文字大小一致 */
.react-datepicker {
    font-size: 12px !important;
}

.react-datepicker__header {
    padding-top: 8px !important;
}

.react-datepicker__month {
    margin: 0 !important;
    padding: 0 !important;
}

.react-datepicker__day-name, .react-datepicker__day {
    width: 1.7rem !important;
    line-height: 1.7rem !important;
    margin: 0.1rem !important;
}

.react-datepicker__current-month {
    font-size: 12px !important;
}

/* 确保日期选择器输入框样式一致 */
.react-datepicker__input-container input {
    width: 100%;
    height: 32px;
    padding: 4px 8px;
    font-size: 12px !important;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-sizing: border-box;
}

/* 禁用状态的输入框样式 */
.react-datepicker__input-container input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}
