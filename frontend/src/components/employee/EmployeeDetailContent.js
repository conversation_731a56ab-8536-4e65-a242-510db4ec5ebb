import React from 'react';
import { Row, Col, Divider } from 'antd';
import './EmployeeDetails.css';

// 判断最高学历是否与第一学历相同
const isSameEducation = (employeeData) => {
    return employeeData.isFinalSameAsFirst === true ||
           (employeeData.finalEducation &&
            employeeData.firstEducation &&
            employeeData.finalEducation === employeeData.firstEducation);
};

// 判断外语是否为英语
const isEnglishLanguage = (languageType) => {
    return languageType === '英语' || languageType === 'English';
};

// 计算司龄的函数
const calculateSeniority = (entryDate, workType, probationEndDate) => {
    if (!entryDate) return '-';
    const today = new Date();
    const startDate = new Date(entryDate);

    // 先判断是否是未来日期
    if (startDate > today) {
        return '未入职';
    }

    // 再判断试用期
    if (workType === '试用' && probationEndDate) {
        const probationEnd = new Date(probationEndDate);
        if (today < probationEnd) {
            return '试用期内';
        }
    }

    // 计算司龄
    const diffTime = today - startDate;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    const days = diffDays % 30;

    // 格式化输出
    let result = '';
    if (years > 0) {
        result += `${years}年`;
    }
    if (months > 0) {
        result += `${months}个月`;
    }
    if (days > 0 && years === 0 && months === 0) {
        result += `${days}天`;
    }

    return result || '不满1天';
};

// 获取职称级别显示
const getTitleLevelDisplay = (employee) => {
    const titles = [];

    // 处理新的数组数据结构
    if (employee.titleLevels && Array.isArray(employee.titleLevels)) {
        // 添加基础职称（无、初级、中级、高级）
        const basicTitles = employee.titleLevels.filter(level => ['无', '初级', '中级', '高级'].includes(level));

        // 检查是否有执业资格
        const hasQualifications = employee.titleLevels.includes('执业资格') &&
                                 employee.professionalQualifications &&
                                 employee.professionalQualifications.length > 0;

        // 如果只有执业资格而没有基础职称，不显示"无"
        if (basicTitles.includes('无') && hasQualifications) {
            // 有执业资格时，不显示"无"
        } else {
            // 添加基础职称
            titles.push(...basicTitles);
        }

        // 添加执业资格
        if (hasQualifications) {
            employee.professionalQualifications.forEach((qual, index) => {
                if (qual === '注册安全工程师') {
                    const level = employee.safetyEngineerLevels && employee.safetyEngineerLevels[index];
                    if (level) {
                        titles.push(`${qual}（${level}）`);
                    } else {
                        titles.push(qual);
                    }
                } else if (qual === '注册建造师') {
                    const level = employee.constructorLevels && employee.constructorLevels[index];
                    const specialty = employee.constructorSpecialties && employee.constructorSpecialties[index];
                    if (level) {
                        const specialtyText = specialty ? specialty : '';
                        titles.push(`${qual}（${level}${specialtyText}）`);
                    } else {
                        titles.push(qual);
                    }
                } else {
                    titles.push(qual);
                }
            });
        }

        return titles.length > 0 ? titles.join('、') : '-';
    }

    // 兼容旧的数据结构
    if (employee.titleLevel === '执业资格') {
        if (employee.professionalQualification === '注册安全工程师') {
            return `${employee.professionalQualification}（${employee.safetyEngineerLevel}）`;
        }
        if (employee.professionalQualification === '注册建造师') {
            return `${employee.professionalQualification}（${employee.constructorLevel}${employee.constructorSpecialty}）`;
        }
        return employee.professionalQualification || '-';
    }
    return employee.titleLevel || '-';
};

const EmployeeDetailContent = ({ employeeData }) => {
    // 统一使用相同的列宽，不再区分打印模式
    const nameColSpan = 4;
    const genderColSpan = 3;
    const bloodTypeColSpan = 3;
    const nationalityColSpan = 3;
    const ethnicityColSpan = 3;
    const idTypeColSpan = 4;
    const idNumberColSpan = 4;
    const contactColSpan = 3;
    const emailColSpan = 4;
    const addressColSpan = 5;
    const educationColSpan = 4;
    const languageColSpan = 3;

    // 工作信息专用列宽
    const workIdColSpan = 3;
    const deptColSpan = 5;
    const positionColSpan = 4;
    const workTypeColSpan = 3;
    const statusColSpan = 3;
    const seniorityColSpan = 3;
    const entryDateColSpan = 4;
    const probationColSpan = 3;
    const probationEndColSpan = 5;
    const adminLevelColSpan = 4;
    const supervisorColSpan = 4;
    const titleLevelColSpan = 4;

    return (
        <div>
            <Divider orientation="center">基本信息</Divider>
            <Row gutter={[16, 16]} justify="center" className="basic-info-row">
                <Col span={nameColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">姓名</div>
                        <div className="employee-card-value">{employeeData.name || '-'}</div>
                    </div>
                </Col>
                <Col span={genderColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">性别</div>
                        <div className="employee-card-value">{employeeData.gender || '-'}</div>
                    </div>
                </Col>
                <Col span={bloodTypeColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">血型</div>
                        <div className="employee-card-value">{employeeData.bloodType || '-'}</div>
                    </div>
                </Col>
                <Col span={nationalityColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">国籍</div>
                        <div className="employee-card-value">{employeeData.nationality || '-'}</div>
                    </div>
                </Col>
                <Col span={ethnicityColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">民族</div>
                        <div className="employee-card-value">{employeeData.ethnicity || '-'}</div>
                    </div>
                </Col>
                <Col span={idTypeColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">证件类型</div>
                        <div className="employee-card-value">{employeeData.idType || '-'}</div>
                    </div>
                </Col>
                <Col span={idNumberColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">证件号码</div>
                        <div className="employee-card-value">{employeeData.idNumber || '-'}</div>
                    </div>
                </Col>
            </Row>
            <Row gutter={[16, 16]} justify="center" style={{ marginTop: '10px' }} className="basic-info-row">
                <Col span={contactColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">证件有效期</div>
                        <div className="employee-card-value">{employeeData.idValidDate || '-'}</div>
                    </div>
                </Col>
                <Col span={contactColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">出生日期</div>
                        <div className="employee-card-value">{employeeData.birthDate || '-'}</div>
                    </div>
                </Col>
                <Col span={contactColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">婚姻状况</div>
                        <div className="employee-card-value">{employeeData.maritalStatus || '-'}</div>
                    </div>
                </Col>
            </Row>
            <Row gutter={[16, 16]} justify="center" style={{ marginTop: '10px' }} className="basic-info-row">
                <Col span={12}>
                    <div className="employee-card">
                        <div className="employee-card-title">开户行</div>
                        <div className="employee-card-value">{employeeData.bankName || '-'}</div>
                    </div>
                </Col>
                <Col span={12}>
                    <div className="employee-card">
                        <div className="employee-card-title">银行卡号</div>
                        <div className="employee-card-value">{employeeData.bankAccount || '-'}</div>
                    </div>
                </Col>
            </Row>

            <Divider orientation="center">联系方式</Divider>
            <Row gutter={[16, 16]} justify="center" className="basic-info-row">
                <Col span={contactColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">联系电话</div>
                        <div className="employee-card-value">{employeeData.phone || '-'}</div>
                    </div>
                </Col>
                <Col span={emailColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">邮箱</div>
                        <div className="employee-card-value">{employeeData.email || '-'}</div>
                    </div>
                </Col>
                <Col span={addressColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">家庭住址</div>
                        <div className="employee-card-value">{employeeData.address || '-'}</div>
                    </div>
                </Col>
            </Row>
            <Row gutter={[16, 16]} justify="center" style={{ marginTop: '10px' }} className="basic-info-row">
                <Col span={contactColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">紧急联系人</div>
                        <div className="employee-card-value">{employeeData.emergencyContact?.name || '-'}</div>
                    </div>
                </Col>
                <Col span={contactColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">与本人关系</div>
                        <div className="employee-card-value">{employeeData.emergencyContact?.relationship || '-'}</div>
                    </div>
                </Col>
                <Col span={contactColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">紧急联系电话</div>
                        <div className="employee-card-value">{employeeData.emergencyContact?.phone || '-'}</div>
                    </div>
                </Col>
                <Col span={contactColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">常用药物及特别说明</div>
                        <div className="employee-card-value">{employeeData.medication || '-'}</div>
                    </div>
                </Col>
            </Row>

            <Divider orientation="center">工作信息</Divider>
            <Row gutter={[16, 16]} justify="center" className="work-info-row-1">
                <Col span={workIdColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">工号</div>
                        <div className="employee-card-value">{employeeData.employeeId || '-'}</div>
                    </div>
                </Col>
                <Col span={deptColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">部门</div>
                        <div className="employee-card-value">{`${employeeData.department || ''}${employeeData.subDepartment ? ` - ${employeeData.subDepartment}` : ''}`}</div>
                    </div>
                </Col>
                <Col span={positionColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">岗位</div>
                        <div className="employee-card-value">{employeeData.position || '-'}</div>
                    </div>
                </Col>
                <Col span={workTypeColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">工作性质</div>
                        <div className="employee-card-value">{employeeData.workType || '-'}</div>
                    </div>
                </Col>
                <Col span={statusColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">在职状态</div>
                        <div className="employee-card-value">{employeeData.status || '-'}</div>
                    </div>
                </Col>
                <Col span={seniorityColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">司龄</div>
                        <div className="employee-card-value">{employeeData.seniority || calculateSeniority(employeeData.entryDate, employeeData.workType, employeeData.probationEndDate)}</div>
                    </div>
                </Col>
            </Row>
            <Row gutter={[16, 16]} justify="center" style={{ marginTop: '10px' }} className="work-info-row-2">
                <Col span={entryDateColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">入职日期</div>
                        <div className="employee-card-value">{employeeData.entryDate || '-'}</div>
                    </div>
                </Col>
                <Col span={probationColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">试用期</div>
                        <div className="employee-card-value">{employeeData.probationMonths === '无' ? '无' : `${employeeData.probationMonths}个月`}</div>
                    </div>
                </Col>
                <Col span={probationEndColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">试用期截止日期</div>
                        <div className="employee-card-value">{employeeData.probationEndDate || '-'}</div>
                    </div>
                </Col>
                <Col span={adminLevelColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">管理岗位级别</div>
                        <div className="employee-card-value">{employeeData.administrativeLevel || '-'}</div>
                    </div>
                </Col>
                <Col span={supervisorColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">主管领导</div>
                        <div className="employee-card-value">{employeeData.supervisor || '-'}</div>
                    </div>
                </Col>
                <Col span={titleLevelColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">职称级别</div>
                        <div className="employee-card-value">{getTitleLevelDisplay(employeeData)}</div>
                    </div>
                </Col>
            </Row>

            <Divider orientation="center">学历信息</Divider>
            <Row gutter={[16, 16]} justify="center" className="education-info-row">
                <Col span={educationColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">第一学历</div>
                        <div className="employee-card-value">{employeeData.firstEducation || '-'}</div>
                    </div>
                </Col>
                <Col span={educationColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">第一学历类型</div>
                        <div className="employee-card-value">{employeeData.firstEducationType || '-'}</div>
                    </div>
                </Col>
                <Col span={educationColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">第一学历院校</div>
                        <div className="employee-card-value">{employeeData.firstSchool || '-'}</div>
                    </div>
                </Col>
                <Col span={educationColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">院校类型</div>
                        <div className="employee-card-value">{employeeData.firstSchoolType || '-'}</div>
                    </div>
                </Col>
                <Col span={educationColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">第一学历专业</div>
                        <div className="employee-card-value">{employeeData.firstMajor || '-'}</div>
                    </div>
                </Col>
                <Col span={educationColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">毕业时间</div>
                        <div className="employee-card-value">{employeeData.firstGraduationDate || '-'}</div>
                    </div>
                </Col>
            </Row>

            {/* 如果第一学历与最高学历相同，显示注释 */}
            {isSameEducation(employeeData) && (
                <Row justify="center" style={{ marginTop: '10px' }}>
                    <Col span={24}>
                        <div style={{ textAlign: 'center', color: '#666', fontStyle: 'italic' }}>
                            注：第一学历是最高学历。
                        </div>
                    </Col>
                </Row>
            )}

            {/* 如果最高学历不同于第一学历，显示最高学历详细信息 */}
            {!isSameEducation(employeeData) && employeeData.finalEducation && (
                <Row gutter={[16, 16]} justify="center" style={{ marginTop: '10px' }} className="education-info-row">
                    <Col span={educationColSpan}>
                        <div className="employee-card">
                            <div className="employee-card-title">最高学历</div>
                            <div className="employee-card-value">{employeeData.finalEducation || '-'}</div>
                        </div>
                    </Col>
                    <Col span={educationColSpan}>
                        <div className="employee-card">
                            <div className="employee-card-title">最高学历类型</div>
                            <div className="employee-card-value">{employeeData.finalEducationType || '-'}</div>
                        </div>
                    </Col>
                    <Col span={educationColSpan}>
                        <div className="employee-card">
                            <div className="employee-card-title">最高学历院校</div>
                            <div className="employee-card-value">{employeeData.finalSchool || '-'}</div>
                        </div>
                    </Col>
                    <Col span={educationColSpan}>
                        <div className="employee-card">
                            <div className="employee-card-title">院校类型</div>
                            <div className="employee-card-value">{employeeData.finalSchoolType || '-'}</div>
                        </div>
                    </Col>
                    <Col span={educationColSpan}>
                        <div className="employee-card">
                            <div className="employee-card-title">最高学历专业</div>
                            <div className="employee-card-value">{employeeData.finalMajor || '-'}</div>
                        </div>
                    </Col>
                    <Col span={educationColSpan}>
                        <div className="employee-card">
                            <div className="employee-card-title">毕业时间</div>
                            <div className="employee-card-value">{employeeData.finalGraduationDate || '-'}</div>
                        </div>
                    </Col>
                </Row>
            )}

            <Divider orientation="center">外语信息</Divider>
            <Row gutter={[16, 16]} justify="center" className="language-info-row">
                <Col span={languageColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">外语语种</div>
                        <div className="employee-card-value">{employeeData.languageType || '-'}</div>
                    </div>
                </Col>
                <Col span={languageColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">外语等级</div>
                        <div className="employee-card-value">{employeeData.languageClass || '-'}</div>
                    </div>
                </Col>

                {/* 只有当外语语种不是英语时，才显示其他语种信息 */}
                {!isEnglishLanguage(employeeData.languageType) && employeeData.otherLanguageType && (
                    <>
                        <Col span={languageColSpan}>
                            <div className="employee-card">
                                <div className="employee-card-title">其他语种</div>
                                <div className="employee-card-value">{employeeData.otherLanguageType || '-'}</div>
                            </div>
                        </Col>
                        <Col span={languageColSpan}>
                            <div className="employee-card">
                                <div className="employee-card-title">语种等级</div>
                                <div className="employee-card-value">{employeeData.otherLanguageClass || '-'}</div>
                            </div>
                        </Col>
                    </>
                )}

                <Col span={languageColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">听力水平</div>
                        <div className="employee-card-value">{employeeData.languageListening || '-'}</div>
                    </div>
                </Col>
                <Col span={languageColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">口语水平</div>
                        <div className="employee-card-value">{employeeData.languageSpeaking || '-'}</div>
                    </div>
                </Col>
                <Col span={languageColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">阅读水平</div>
                        <div className="employee-card-value">{employeeData.languageReading || '-'}</div>
                    </div>
                </Col>
                <Col span={languageColSpan}>
                    <div className="employee-card">
                        <div className="employee-card-title">写作水平</div>
                        <div className="employee-card-value">{employeeData.languageWriting || '-'}</div>
                    </div>
                </Col>
            </Row>
        </div>
    );
};

export default EmployeeDetailContent;
