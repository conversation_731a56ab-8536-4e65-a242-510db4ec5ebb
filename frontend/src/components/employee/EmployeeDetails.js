import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { message, Modal, Row, Col, Divider } from 'antd';
import './EmployeeDetails.css';

import ExportDialog from '../../common/ExportDialog';
import ExportUtils from '../../common/ExportUtils';
import config from '../../config';
import EmployeeDetailContent from './EmployeeDetailContent';

function EmployeeDetails({ visible, employee, onClose }) {
    const { id } = useParams();
    const navigate = useNavigate();
    const [employeeData, setEmployeeData] = useState(null);
    const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);

    // 判断是否为弹窗模式
    const isModalMode = visible !== undefined && employee !== undefined && onClose !== undefined;
    const getTitleLevelDisplay = (employee) => {
        const titles = [];

        // 处理新的数组数据结构
        if (employee.titleLevels && Array.isArray(employee.titleLevels)) {
            // 添加基础职称（无、初级、中级、高级）
            const basicTitles = employee.titleLevels.filter(level => ['无', '初级', '中级', '高级'].includes(level));

            // 检查是否有执业资格
            const hasQualifications = employee.titleLevels.includes('执业资格') &&
                                     employee.professionalQualifications &&
                                     employee.professionalQualifications.length > 0;

            // 如果只有执业资格而没有基础职称，不显示"无"
            if (basicTitles.includes('无') && hasQualifications) {
                // 有执业资格时，不显示"无"
            } else {
                // 添加基础职称
                titles.push(...basicTitles);
            }

            // 添加执业资格
            if (hasQualifications) {
                employee.professionalQualifications.forEach((qual, index) => {
                    if (qual === '注册安全工程师') {
                        const level = employee.safetyEngineerLevels && employee.safetyEngineerLevels[index];
                        if (level) {
                            titles.push(`${qual}（${level}）`);
                        } else {
                            titles.push(qual);
                        }
                    } else if (qual === '注册建造师') {
                        const level = employee.constructorLevels && employee.constructorLevels[index];
                        const specialty = employee.constructorSpecialties && employee.constructorSpecialties[index];
                        if (level) {
                            const specialtyText = specialty ? specialty : '';
                            titles.push(`${qual}（${level}${specialtyText}）`);
                        } else {
                            titles.push(qual);
                        }
                    } else {
                        titles.push(qual);
                    }
                });
            }

            return titles.length > 0 ? titles.join('、') : '-';
        }

        // 兼容旧的数据结构
        if (employee.titleLevel === '执业资格') {
            if (employee.professionalQualification === '注册安全工程师') {
                return `${employee.professionalQualification}（${employee.safetyEngineerLevel}）`;
            }
            if (employee.professionalQualification === '注册建造师') {
                return `${employee.professionalQualification}（${employee.constructorLevel}${employee.constructorSpecialty}）`;
            }
            return employee.professionalQualification || '-';
        }
        return employee.titleLevel || '-';
    };

    useEffect(() => {
        // 如果是弹窗模式，直接使用传入的员工数据
        if (isModalMode && employee) {
            setEmployeeData(employee);
            return;
        }

        // 如果是路由模式，从API获取员工数据
        const fetchEmployeeDetails = async () => {
            try {
                const response = await fetch(`${config.apiBaseUrl}/employees/${id}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json'
                    }
                });
                if (!response.ok) {
                    message.error('获取员工信息失败');
                    return;
                }
                const data = await response.json();
                if (!data) {
                    message.error('未找到员工信息');
                    return;
                }
                setEmployeeData(data);
            } catch (error) {
                console.error('获取员工详情失败:', error);
                message.error('获取员工信息失败，请稍后重试');
                setEmployeeData(null);
            }
        };

        if (id) {
            fetchEmployeeDetails();
        }
    }, [id, isModalMode, employee]);

    const handleExportWithFormat = (fileName, fileFormat) => {
        if (!employeeData) return;

        // 创建扁平化数据结构，直接适用于导出
        const flatData = [
            // 基本信息部分
            { item: '基本信息', value: '', isTitle: true },
            { item: '姓名', value: employeeData.name || '-', isTitle: false },
            { item: '性别', value: employeeData.gender || '-', isTitle: false },
            { item: '工号', value: employeeData.employeeId || '-', isTitle: false },
            { item: '血型', value: employeeData.bloodType || '-', isTitle: false },
            { item: '国籍', value: employeeData.nationality || '-', isTitle: false },
            { item: '民族', value: employeeData.ethnicity || '-', isTitle: false },
            { item: '证件类型', value: employeeData.idType || '-', isTitle: false },
            { item: '证件号码', value: employeeData.idNumber || '-', isTitle: false },
            { item: '证件有效期', value: employeeData.idValidDate || '-', isTitle: false },
            { item: '出生日期', value: employeeData.birthDate || '-', isTitle: false },
            { item: '婚姻状况', value: employeeData.maritalStatus || '-', isTitle: false },

            // 联系方式部分
            { item: '联系方式', value: '', isTitle: true },
            { item: '联系电话', value: employeeData.phone || '-', isTitle: false },
            { item: '邮箱', value: employeeData.email || '-', isTitle: false },
            { item: '家庭住址', value: employeeData.address || '-', isTitle: false },
            { item: '紧急联系人', value: employeeData.emergencyContact?.name || '-', isTitle: false },
            { item: '与本人关系', value: employeeData.emergencyContact?.relationship || '-', isTitle: false },
            { item: '紧急联系电话', value: employeeData.emergencyContact?.phone || '-', isTitle: false },
            { item: '常用药物及特别说明', value: employeeData.medication || '-', isTitle: false },

            // 银行信息部分
            { item: '银行信息', value: '', isTitle: true },
            { item: '开户行', value: employeeData.bankName || '-', isTitle: false },
            { item: '银行卡号', value: employeeData.bankAccount || '-', isTitle: false },

            // 工作信息部分
            { item: '工作信息', value: '', isTitle: true },
            { item: '工作性质', value: employeeData.workType || '-', isTitle: false },
            { item: '在职状态', value: employeeData.status || '-', isTitle: false },
            { item: '入职日期', value: employeeData.entryDate || '-', isTitle: false },
            { item: '试用期', value: employeeData.probationMonths === '无' ? '无' : `${employeeData.probationMonths}个月`, isTitle: false },
            { item: '试用期截止日期', value: employeeData.probationEndDate || '-', isTitle: false },
            { item: '司龄', value: employeeData.seniority || '-', isTitle: false },
            { item: '岗位名称', value: employeeData.position || '-', isTitle: false },
            { item: '管理岗位级别', value: employeeData.administrativeLevel || '-', isTitle: false },
            { item: '所属部门', value: `${employeeData.department || ''}${employeeData.subDepartment ? ` - ${employeeData.subDepartment}` : ''}`, isTitle: false },
            { item: '主管领导', value: employeeData.supervisor || '-', isTitle: false },
            { item: '职称级别', value: getTitleLevelDisplay(employeeData), isTitle: false },

            // 学历信息部分
            { item: '学历信息', value: '', isTitle: true },
            { item: '第一学历', value: employeeData.firstEducation || '-', isTitle: false },
            { item: '第一学历类型', value: employeeData.firstEducationType || '-', isTitle: false },
            { item: '第一学历院校', value: employeeData.firstSchool || '-', isTitle: false },
            { item: '院校类型', value: employeeData.firstSchoolType || '-', isTitle: false },
            { item: '第一学历专业', value: employeeData.firstMajor || '-', isTitle: false },
            { item: '第一学历毕业时间', value: employeeData.firstGraduationDate || '-', isTitle: false },
            { item: '最高学历', value: (employeeData.isFinalSameAsFirst === true ||
                (employeeData.finalEducation && employeeData.firstEducation &&
                 employeeData.finalEducation === employeeData.firstEducation)) ?
                '与第一学历相同' : (employeeData.finalEducation || '-'), isTitle: false },
        ];

        // 如果最高学历不同于第一学历，添加最高学历详细信息
        if (!(employeeData.isFinalSameAsFirst === true ||
              (employeeData.finalEducation && employeeData.firstEducation &&
               employeeData.finalEducation === employeeData.firstEducation))) {
            flatData.push(
                { item: '最高学历类型', value: employeeData.finalEducationType || '-', isTitle: false },
                { item: '最高学历院校', value: employeeData.finalSchool || '-', isTitle: false },
                { item: '最高学历院校类型', value: employeeData.finalSchoolType || '-', isTitle: false },
                { item: '最高学历专业', value: employeeData.finalMajor || '-', isTitle: false },
                { item: '最高学历毕业时间', value: employeeData.finalGraduationDate || '-', isTitle: false }
            );
        }

        // 外语信息部分
        flatData.push(
            { item: '外语信息', value: '', isTitle: true },
            { item: '外语语种', value: employeeData.languageType || '-', isTitle: false },
            { item: '外语等级', value: employeeData.languageClass || '-', isTitle: false }
        );

        // 只有当外语语种不是英语时，才添加其他语种信息
        if (employeeData.languageType !== '英语' && employeeData.languageType !== 'English') {
            flatData.push(
                { item: '其他语种', value: employeeData.otherLanguageType || '-', isTitle: false },
                { item: '语种等级', value: employeeData.otherLanguageClass || '-', isTitle: false }
            );
        }

        // 添加语言能力评估
        flatData.push(
            { item: '听力水平', value: employeeData.languageListening || '-', isTitle: false },
            { item: '口语水平', value: employeeData.languageSpeaking || '-', isTitle: false },
            { item: '阅读水平', value: employeeData.languageReading || '-', isTitle: false },
            { item: '写作水平', value: employeeData.languageWriting || '-', isTitle: false }
        );

        const options = {
            title: '员工详细信息',
            headers: ['项目', '内容'],
            fields: ['item', 'value']
        };

        console.log('导出数据:', flatData);

        try {
            switch (fileFormat) {
                case 'xlsx':
                case 'xls': {
                    ExportUtils.exportAsExcel(flatData, fileName, options);
                    break;
                }
                case 'pdf': {
                    ExportUtils.exportAsPDF(flatData, fileName, options);
                    break;
                }
                case 'doc':
                case 'docx': {
                    ExportUtils.exportAsWord(flatData, fileName, options);
                    break;
                }
                default:
                    console.error('不支持的文件格式');
            }
            message.success(`导出${fileFormat}文件成功`);
        } catch (error) {
            console.error('导出失败:', error);
            message.error('导出失败: ' + error.message);
        }

        setIsExportDialogOpen(false);
    };

    // 如果是弹窗模式，使用Modal组件
    if (isModalMode) {
        return (
            <div>
                <ExportDialog
                    isOpen={isExportDialogOpen}
                    onClose={() => setIsExportDialogOpen(false)}
                    onExport={handleExportWithFormat}
                    moduleType="员工详情"
                />
                <Modal
                    title="员工详细信息"
                    open={visible}
                    onCancel={onClose}
                    destroyOnClose={true}
                    footer={
                        <div className="modal-footer-buttons">
                            <button
                                key="export"
                                className="ant-btn ant-btn-primary"
                                style={{ marginRight: '8px', backgroundColor: '#1890ff', color: 'white', border: 'none', height: '32px', padding: '0 15px' }}
                                onClick={() => setIsExportDialogOpen(true)}
                            >
                                导出
                            </button>
                            <button
                                className="ant-btn ant-btn-primary print-button"
                                style={{
                                    marginRight: '8px',
                                    backgroundColor: '#10b981',
                                    color: 'white',
                                    border: 'none',
                                    height: '32px',
                                    padding: '0 15px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '5px'
                                }}
                                onClick={() => {
                                    // 最简单的打印方法
                                    window.print();
                                }}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <polyline points="6 9 6 2 18 2 18 9"></polyline>
                                    <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                                    <rect x="6" y="14" width="12" height="8"></rect>
                                </svg>
                                打印
                            </button>
                            <button
                                key="close"
                                className="ant-btn"
                                style={{ backgroundColor: '#f59e0b', color: 'white', border: 'none', height: '32px', padding: '0 15px' }}
                                onClick={onClose}
                            >
                                关闭
                            </button>
                        </div>
                    }
                    className="employee-detail-modal"
                    width={800}
                    style={{
                        top: '2%',
                        marginBottom: '40px'
                    }}
                    styles={{
                        body: {
                            padding: '24px',
                            width: '100%',
                            maxHeight: '80vh',
                            overflowY: 'auto'
                        },
                        content: {
                            width: '860px'
                        }
                    }}
                    centered
                >
                    {/* 弹窗内容 - 使用可复用的组件 */}
                    {employeeData ? (
                        <div className="details-content" style={{ width: '100%', maxWidth: '100%', boxSizing: 'border-box' }}>
                            <EmployeeDetailContent employeeData={employeeData} />
                        </div>
                    ) : (
                        <div className="loading">加载中...</div>
                    )}
                </Modal>
            </div>
        );
    }

    // 如果是路由模式，使用原来的布局
    return (
        <div className="employees-container">
            <ExportDialog
                isOpen={isExportDialogOpen}
                onClose={() => setIsExportDialogOpen(false)}
                onExport={handleExportWithFormat}
                moduleType="员工详情"
            />
            <header className="employees-header">
                <div className="management-bar">
                    <div className="left-buttons">
                        <button className="back-button blue" onClick={() => navigate('/employeeList')}>
                            <i className="back-icon">←</i>
                            返回
                        </button>
                    </div>
                    <div className="right-buttons">
                        <button
                            className="export-button"
                            onClick={() => setIsExportDialogOpen(true)}
                        >
                            <i className="export-icon">↓</i>
                            导出详情
                        </button>
                        <button
                            className="print-button"
                            onClick={() => {
                                // 最简单的打印方法
                                window.print();
                            }}
                        >
                            <i className="print-icon">🖨</i>
                            打印
                        </button>
                    </div>
                </div>
            </header>
            <div className="content-wrapper">
                <h2 style={{ textAlign: 'center', margin: '20px 0' }}>员工详细信息</h2>
                {employeeData ? (
                    <div className="details-content">
                        <EmployeeDetailContent employeeData={employeeData} />
                    </div>
                ) : (
                    <div className="loading">加载中...</div>
                )}
            </div>
        </div>
    );
}

// 添加 CSS 样式
const modalStyle = document.createElement('style');
modalStyle.innerHTML = `
.employee-detail-modal .ant-modal-content {
    border-radius: 8px;
}
.employee-detail-modal .ant-modal-header,
.employee-detail-modal .ant-modal-title {
    text-align: center;
}
.employee-detail-modal {
    z-index: 1000;
}
.employee-detail-modal .ant-modal-body {
    max-height: calc(80vh - 120px);
    overflow-y: auto;
    padding: 12px 24px;
}
.employee-detail-modal .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 10px 16px;
    text-align: center;
    display: flex;
    justify-content: center;
}
`;
document.head.appendChild(modalStyle);

export default EmployeeDetails;
