import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import './EmployeeForm.css';
import './DatePickerStyles.css';
import config from '../../config';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import moment from 'moment';

function EmployeeForm() {
    const parseDate = (dateStr) => dateStr ? new Date(dateStr) : null;
    const { id } = useParams();  // 获取路由参数中的 id
    const navigate = useNavigate();
    // 添加计算司龄的函数（放在其他工具函数附近）
    const calculateSeniority = (entryDate, workType, probationEndDate) => {
        if (!entryDate) return '-';
        const today = new Date();
        const startDate = new Date(entryDate);

        // 先判断是否是未来日期
        if (startDate > today) {
            return '未入职';
        }

        // 再判断试用期
        if (workType === '试用' && probationEndDate) {
            const probationEnd = new Date(probationEndDate);
            if (today < probationEnd) {
                return '试用期内';
            }
        }

        // 计算司龄
        const diffTime = today - startDate;
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        const years = Math.floor(diffDays / 365);
        const months = Math.floor((diffDays % 365) / 30);
        const days = diffDays % 30;

        // 格式化输出
        let result = '';
        if (years > 0) {
            result += `${years}年`;
        }
        if (months > 0) {
            result += `${months}个月`;
        }
        if (days > 0 && years === 0 && months === 0) {
            result += `${days}天`;
        }

        return result || '不满1天';
    };

    useEffect(() => {
        const handleClickOutside = (event) => {
            const titleLevelsDropdown = event.target.closest('.title-levels-dropdown');
            const qualificationMenu = event.target.closest('.qualification-menu');
            const qualificationSelect = event.target.closest('.qualification-select');

            if (!titleLevelsDropdown && !qualificationMenu && !qualificationSelect) {
                setFormData(prev => ({
                    ...prev,
                    showTitleLevelsMenu: false,
                    showQualificationMenu: false,
                    // 如果是执业资格但没有选择具体资格，则重置为空
                    ...(prev.titleLevels.includes('执业资格') && prev.professionalQualifications.length === 0 ? {
                        titleLevels: prev.titleLevels.filter(level => level !== '执业资格'),
                        professionalQualifications: [],
                        safetyEngineerLevels: [],
                        constructorLevels: [],
                        constructorSpecialties: []
                    } : {})
                }));
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            // 更精确的选择器，避免与日期选择器冲突
            if (!event.target.closest('.form-group') ||
                event.target.closest('.react-datepicker') ||
                event.target.closest('.react-datepicker-popper') ||
                event.target.closest('.custom-popper-container')) {
                return;
            }
            setFormData(prev => ({
                ...prev,
                showValidDateSelect: false
            }));
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            // 检查是否点击了部门选择相关的元素
            const departmentSelect = event.target.closest('.department-select');
            const departmentMenu = event.target.closest('.department-menu');
            const departmentOption = event.target.closest('.department-option');

            // 如果点击的是部门选择相关元素，不关闭菜单
            if (departmentSelect || departmentMenu || departmentOption) {
                return;
            }

            // 检查是否点击了日期选择器相关元素
            if (event.target.closest('.react-datepicker') ||
                event.target.closest('.react-datepicker-popper') ||
                event.target.closest('.custom-popper-container')) {
                return;
            }

            // 关闭部门选择菜单
            setFormData(prev => ({
                ...prev,
                showDepartmentSelect: false
            }));
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    // eslint-disable-next-line no-unused-vars
    const qualificationOptions = [
        { value: '注册化工工程师', label: '注册化工工程师' },
        { value: '注册电气工程师', label: '注册电气工程师' },
        { value: '注册环保工程师', label: '注册环保工程师' },
        {
            value: '注册安全工程师',
            label: '注册安全工程师',
            subOptions: [
                { value: '初级', label: '初级' },
                { value: '中级', label: '中级' },
                { value: '高级', label: '高级' }
            ]
        },
        { value: '注册造价工程师', label: '注册造价工程师' },
        { value: '注册监理工程师', label: '注册监理工程师' },
        {
            value: '注册建造师',
            label: '注册建造师',
            subOptions: [
                {
                    value: '一级',
                    label: '一级',
                    specialties: [
                        { value: '市政', label: '市政' },
                        { value: '机电', label: '机电' },
                        { value: '建筑', label: '建筑' }
                    ]
                },
                {
                    value: '二级',
                    label: '二级',
                    specialties: [
                        { value: '市政', label: '市政' },
                        { value: '机电', label: '机电' },
                        { value: '建筑', label: '建筑' }
                    ]
                }
            ]
        },
        { value: '注册会计师', label: '注册会计师' }
    ];

    // 将 departments 改为数组格式
    const departments = [
        {
            value: '工程部',
            label: '工程部',
            subOptions: [
                { value: '技术科', label: '技术科' },
                { value: '项目管理科', label: '项目管理科' }
            ]
        },
        {
            value: '商贸部',
            label: '商贸部',
        },
        {
            value: '供应管理部',
            label: '供应管理部',
        },
        {
            value: '财务部',
            label: '财务部'
        },
        {
            value: '行政部',
            label: '行政部',
            subOptions: [
                { value: '行政管理', label: '行政管理' },
                { value: '综合办', label: '综合办' }
            ]
        }
    ];


        // 修改 handleDepartmentSelect 函数
    const handleDepartmentSelect = (dept, subDept = null) => {
        setFormData(prev => ({
            ...prev,
            department: dept.label,
            subDepartment: subDept ? subDept.label : '',  // 如果没有选择子部门，就设为空字符串
            showDepartmentSelect: false
        }));
    };
    // 添加处理资格选择的函数
    const handleQualificationSelect = (qualType, level, specialty = null) => {
        setFormData(prev => {
            // 检查是否已经选择了这个资格类型和级别的组合
            if (qualType === '注册安全工程师') {
                const existingIndex = prev.professionalQualifications.findIndex((qual, index) =>
                    qual === qualType && prev.safetyEngineerLevels[index] === level.value
                );
                if (existingIndex !== -1) {
                    return prev; // 如果已存在相同的组合，不做任何操作
                }
            } else if (qualType === '注册建造师') {
                const constructorLevel = specialty ? (prev.tempConstructorLevel || level.value) : level.value;
                const constructorSpecialty = specialty ? specialty.value : '';
                const existingIndex = prev.professionalQualifications.findIndex((qual, index) =>
                    qual === qualType &&
                    prev.constructorLevels[index] === constructorLevel &&
                    prev.constructorSpecialties[index] === constructorSpecialty
                );
                if (existingIndex !== -1) {
                    return prev; // 如果已存在相同的组合，不做任何操作
                }
            }

            // 当添加执业资格时，如果当前只有"无"，应该移除"无"
            let newTitleLevels;
            if (prev.titleLevels.includes('执业资格')) {
                newTitleLevels = prev.titleLevels;
            } else {
                // 如果当前只有"无"，移除"无"并添加"执业资格"
                if (prev.titleLevels.length === 1 && prev.titleLevels.includes('无')) {
                    newTitleLevels = ['执业资格'];
                } else {
                    newTitleLevels = [...prev.titleLevels, '执业资格'];
                }
            }

            if (qualType === '注册安全工程师') {
                return {
                    ...prev,
                    titleLevels: newTitleLevels,
                    professionalQualifications: [...prev.professionalQualifications, qualType],
                    safetyEngineerLevels: [...prev.safetyEngineerLevels, level.value],
                    showQualificationMenu: false
                };
            } else if (qualType === '注册建造师') {
                // 使用临时存储的级别信息或当前传入的级别信息
                const constructorLevel = specialty ? (prev.tempConstructorLevel || level.value) : level.value;
                return {
                    ...prev,
                    titleLevels: newTitleLevels,
                    professionalQualifications: [...prev.professionalQualifications, qualType],
                    constructorLevels: [...prev.constructorLevels, constructorLevel],
                    constructorSpecialties: [...prev.constructorSpecialties, specialty ? specialty.value : ''],
                    showQualificationMenu: false,
                    tempConstructorLevel: undefined // 清除临时存储
                };
            }
            return prev;
        });
    };
    // 计算试用期结束日期
    const calculateProbationEndDate = (entryDate, months) => {
        if (!entryDate || !months || months === '无') return '';
        const date = new Date(entryDate);
        date.setMonth(date.getMonth() + parseInt(months));
        // 确保返回的日期格式为 YYYY-MM-DD
        return date.getFullYear() + '-' +
               String(date.getMonth() + 1).padStart(2, '0') + '-' +
               String(date.getDate()).padStart(2, '0');
    };

    // 添加身份证验证函数
    const validateIdNumber = (idNumber) => {
        // 如果不是中国籍或不是居民身份证，直接返回空字符串（表示验证通过）
        if ((formData.nationality !== '中国' && formData.nationality !== '中华人民共和国') ||
            formData.idType !== '居民身份证') {
            return '';
        }

        // 以下是中国居民身份证的验证逻辑
        const idPattern = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;
        if (!idPattern.test(idNumber)) {
            return '请输入正确的身份证号码';
        }

        // 验证身份证校验码
        const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        let sum = 0;
        for (let i = 0; i < 17; i++) {
            sum += parseInt(idNumber[i]) * factor[i];
        }
        if (parity[sum % 11] !== idNumber[17].toString()) {
            return '请输入正确的身份证号码';
        }

        // 验证年龄
        const birthYear = parseInt(idNumber.substring(6, 10));
        const birthMonth = parseInt(idNumber.substring(10, 12));
        const birthDay = parseInt(idNumber.substring(12, 14));

        const today = new Date();
        let age = today.getFullYear() - birthYear;
        const monthDiff = today.getMonth() - (birthMonth - 1);
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDay)) {
            age--;
        }
        if (age < 18) {
            return '员工必须年满18岁';
        }

        return '';
    };

    // 修改年龄验证函数
    const validateAge = (birthDate) => {
        if (!birthDate) return null;
        const today = new Date();
        const birth = new Date(birthDate);

        // 检查日期是否有效
        if (isNaN(birth.getTime())) return null;

        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }

        // 如果年龄为负数或超过合理范围，返回null
        return (age < 0 || age > 150) ? null : age;
    };

    // 修改年龄限制验证函数
    const checkAgeLimit = (age) => {
        if (age === null) return '请输入有效的出生日期';
        return age < 18 ? '员工必须年满18岁' : null;
    };

    // 添加提取出生日期的函数
    const extractBirthDate = (idNumber) => {
        if (idNumber && idNumber.length >= 14) {
            const year = idNumber.substring(6, 10);
            const month = idNumber.substring(10, 12);
            const day = idNumber.substring(12, 14);
            return `${year}-${month}-${day}`;
        }
        return '';
    };

    // 处理日期输入的实时匹配
    const handleDateInputMatch = (value, fieldName) => {
        // 保存用户输入
        setFormData(prev => ({
            ...prev,
            [fieldName]: value
        }));

        // 尝试匹配年份
        if (value.length >= 4) {
            const yearStr = value.substring(0, 4);
            const year = parseInt(yearStr, 10);

            // 检查是否是有效年份
            if (!isNaN(year) && year >= 1900 && year <= 2100) {
                let month = 1;
                let day = 1;

                // 检查是否有分隔符
                const hasSeparator = value.length > 4 && (value[4] === '-' || value[4] === '/');

                // 尝试匹配月份 (处理 YYYY-M, YYYY-MM, YYYY/M, YYYY/MM 或 YYYYMM 格式)
                if (value.length >= 5) {
                    let monthStr;

                    if (hasSeparator) {
                        // 处理带分隔符的格式 (YYYY-MM 或 YYYY/MM)
                        if (value.length >= 6) {
                            monthStr = value.substring(5);
                            // 如果有第二个分隔符，只取分隔符前的部分
                            const secondSeparatorIndex = monthStr.indexOf('-') !== -1 ?
                                monthStr.indexOf('-') : monthStr.indexOf('/');
                            if (secondSeparatorIndex !== -1) {
                                monthStr = monthStr.substring(0, secondSeparatorIndex);
                            }
                        }
                    } else {
                        // 处理无分隔符的格式 (YYYYMM)
                        monthStr = value.substring(4);
                        if (monthStr.length > 2) {
                            monthStr = monthStr.substring(0, 2);
                        }
                    }

                    if (monthStr && monthStr.length > 0) {
                        month = parseInt(monthStr, 10);
                        if (isNaN(month) || month < 1 || month > 12) {
                            month = 1;
                        }
                    }

                    // 尝试匹配日期
                    if (hasSeparator) {
                        // 查找第二个分隔符
                        const secondSeparatorIndex = value.indexOf('-', 5) !== -1 ?
                            value.indexOf('-', 5) : value.indexOf('/', 5);

                        if (secondSeparatorIndex !== -1 && value.length > secondSeparatorIndex) {
                            // 有第二个分隔符，提取日期部分
                            const dayStr = value.substring(secondSeparatorIndex + 1);
                            if (dayStr.length > 0) {
                                day = parseInt(dayStr, 10);
                                const daysInMonth = new Date(year, month, 0).getDate();
                                if (isNaN(day) || day < 1 || day > daysInMonth) {
                                    day = 1;
                                }
                            }
                        }
                    } else if (value.length >= 6) {
                        // 处理无分隔符的格式 (YYYYMM 或 YYYYMMDD)
                        if (value.length >= 8) {
                            // 完整的 YYYYMMDD 格式
                            const dayStr = value.substring(6);
                            if (dayStr.length > 0) {
                                day = parseInt(dayStr, 10);
                                const daysInMonth = new Date(year, month, 0).getDate();
                                if (isNaN(day) || day < 1 || day > daysInMonth) {
                                    day = 1;
                                }
                            }
                        } else if (value.length === 6 || value.length === 7) {
                            // YYYYMM 格式，日期默认为1
                            day = 1;
                        }
                    }
                }

                // 创建日期对象并更新表单
                const date = moment([year, month - 1, day]);

                // 格式化日期为 YYYY-MM-DD 格式
                const formattedDate = date.format('YYYY-MM-DD');

                // 更新表单字段
                setFormData(prev => ({
                    ...prev,
                    [fieldName]: formattedDate
                }));

                // 在控制台显示匹配的日期（用于调试）
                console.log(`匹配日期: ${year}-${month}-${day}, 格式化日期: ${formattedDate}, 字段: ${fieldName}`);
            }
        }
    };

    const validateEducation = (name, value) => {
        const educationLevels = {
            '专科': 1,
            '本科': 2,
            '硕士': 3,
            '博士': 4
        };

        if (name === 'education' && formData.firstEducation) {
            if (educationLevels[value] < educationLevels[formData.firstEducation]) {
                alert('最高学历不能低于第一学历');
                return false;
            }
        }
        return true;
    };

    /// 修改 handleChange 函数中处理生日的部分
    const handleChange = (e) => {
        const { name, value } = e.target;

        // 处理紧急联系人信息
        if (name.startsWith('emergencyContact.')) {
            const field = name.split('.')[1];
            setFormData(prev => ({
                ...prev,
                emergencyContact: {
                    ...prev.emergencyContact,
                    [field]: value
                }
            }));
            return;
        }

        // 其余原有逻辑保持不变
        if (name === 'entryDate' || name === 'workType' || name === 'probationMonths') {
            setFormData(prev => {
                const newData = { ...prev, [name]: value };
                newData.seniority = calculateSeniority(
                    newData.entryDate,
                    newData.workType,
                    newData.probationEndDate
                );
                return newData;
            });
            return;
        }
        if (name === 'finalEducation' && value === 'same') {
            setFormData(prev => ({
                ...prev,
                finalEducation: value,
                finalEducationType: prev.firstEducationType,
                finalSchool: prev.firstSchool,
                finalSchoolType: prev.firstSchoolType,
                finalMajor: prev.firstMajor,
                finalGraduationDate: prev.firstGraduationDate,
                isFinalSameAsFirst: true
            }));
            return;
        } else if (name === 'finalEducation' && value !== 'same') {
            setFormData(prev => ({
                ...prev,
                [name]: value,
                isFinalSameAsFirst: false
            }));
            return;
        }
        if ((name === 'firstEducationType' || name === 'firstSchool' ||
             name === 'firstSchoolType' || name === 'firstMajor' ||
             name === 'firstGraduationDate') && formData.finalEducation === 'same') {
            const fieldMap = {
                'firstEducationType': 'finalEducationType',
                'firstSchool': 'finalSchool',
                'firstSchoolType': 'finalSchoolType',
                'firstMajor': 'finalMajor',
                'firstGraduationDate': 'finalGraduationDate'
            };
            setFormData(prev => ({
                ...prev,
                [name]: value,
                [fieldMap[name]]: value
            }));
            return;
        }
            // 处理国籍变化
            if (name === 'nationality') {
                const isChina = value === '中国' || value === '中华人民共和国';
                setFormData(prev => ({
                    ...prev,
                    [name]: value,
                    idLabel: isChina ? '身份证号码' : '证件号码',
                    idType: isChina ? '居民身份证' : '',
                    idNumber: '',
                    birthDate: ''
                }));
                return;
            }

            // 处理证件类型变化
            if (name === 'idType') {
                setFormData(prev => ({
                    ...prev,
                    [name]: value,
                    idNumber: '',
                    birthDate: ''
                }));
                return;
            }

            // 处理身份证号输入
            if (name === 'idNumber') {
                // 如果是中国籍且是居民身份证，进行验证
                if ((formData.nationality === '中国' || formData.nationality === '中华人民共和国') &&
                    formData.idType === '居民身份证') {
                    if (!/^[\dX]*$/.test(value)) return;
                    if (value.length > 18) return;

                    if (value.length === 18) {
                        const errorMsg = validateIdNumber(value);
                        if (errorMsg) {
                            alert(errorMsg);
                            return;
                        }
                    }
                }
                // 非中国籍或非居民身份证时，允许输入任意字符
                setFormData(prev => ({
                    ...prev,
                    [name]: value,
                    birthDate: (value.length === 18 && formData.idType === '居民身份证') ?
                        extractBirthDate(value) : prev.birthDate
                }));
                return;
            }
            if (name === 'birthDate' && formData.idType !== '居民身份证') {
                const ageError = validateAge(value);
                if (ageError) {
                    alert(ageError);
                 return;
                }
                setFormData(prev => ({
                    ...prev,
                   [name]: value
                }));
                return;
            }

            // 处理教育相关字段变化
            if (name === 'finalEducation') {
                if (!validateEducation(name, value)) {
                    return;
                }
            }

            if (name === 'languageType' && value === '无') {
                setFormData(prev => ({
                    ...prev,
                    [name]: value,
                    languageClass: '无',
                    otherLanguageType: '',
                    otherLanguageClass: '',
                    languageListening: '无',
                    languageSpeaking: '无',
                    languageReading: '无',
                    languageWriting: '无'
                }));
                return;
            }

                // 处理其他字段变化
                setFormData(prev => {
                    // 处理工作性质变化
                    if (name === 'workType') {
                        // ... 工作性质相关代码保持不变 ...
                    }

                    const newData = { ...prev, [name]: value };
                    // ... 其他代码保持不变 ...
                    return newData;
                });

                    // 处理紧急联系人信息
                    if (name.startsWith('emergencyContact.')) {
                        const field = name.split('.')[1];
                        setFormData(prev => ({
                            ...prev,
                            emergencyContact: {
                                ...prev.emergencyContact,
                                [field]: value
                            }
                        }));
                        return;
                    }
                    // 处理其他字段变化

                    // 处理其他字段变化
                    setFormData(prev => {
                        // 处理工作性质变化
                        if (name === 'workType') {
                            if (id && value === '试用' && (prev.status === '全职' || prev.status === '兼职')) {
                                alert('不可修改为试用状态');
                                return prev;  // 保持原状态不变
                            }

                            const newData = { ...prev, [name]: value };
                            if (value !== '试用') {
                                newData.probationMonths = '无';
                                newData.probationEndDate = '';
                            } else {
                                const today = new Date().toISOString().split('T')[0];
                                newData.entryDate = today;
                                newData.probationMonths = '无';
                                newData.probationEndDate = calculateProbationEndDate(today, '');
                            }
                            return newData;
                        }

                // ... 其他逻辑保持不变 ...
                const newData = { ...prev, [name]: value };

                // 处理入职日期或试用期月数变化
                if ((name === 'entryDate' || name === 'probationMonths') && newData.workType === '试用') {
                    newData.probationEndDate = calculateProbationEndDate(
                    newData.entryDate,
                        newData.probationMonths
                    );
                }

            // 处理岗位变化
            if (name === 'position') {
                newData.administrativeLevel = value === '行政管理' ? '' : '无';
            }

            // 处理管理岗位级别变化
            if (name === 'administrativeLevel') {
                if (value === '总经理' || value === '副总经理') {
                    newData.department = '全公司';
                    newData.subDepartment = '';
                    newData.showDepartmentSelect = false;
                } else if (prev.position !== '行政管理') {
                    newData.administrativeLevel = '无';
                }
            }
            return newData;
        });
    };

    // 在 state 中添加 birthDate 字段
    const [formData, setFormData] = useState({
        // 个人信息
        name: '',
        gender: '',
        bloodType: '',
        nationality: '',
        ethnicity: '',
        idType: '',
        idNumber: '',
        idValidDate: '',
        idValidType: 'temporary', // 证件有效期类型：'permanent'(长期) 或 'temporary'(定期)
        birthDate: '',
        birthplace: '',
        email: '',
        phone: '',
        maritalStatus: '',
        address: '',
        medication: '',
        bankName: '',     // 开户行
        bankAccount: '',  // 银行卡号
        emergencyContact: {
            name: '',
            phone: '',
            relationship: ''
        },  // 添加逗号

        // 学历信息
        firstEducation: '',
        firstEducationType: '',
        firstSchool: '',
        firstSchoolType: '',    // 第一学历院校类型
        firstMajor: '',
        firstGraduationDate: '',
        finalEducation: '',
        finalEducationType: '',
        finalSchool: '',
        finalSchoolType: '',    // 最高学历院校类型
        finalMajor: '',
        finalGraduationDate: '',
        isFinalSameAsFirst: false,  // 新增字段，标记最高学历是否与第一学历相同

        // 添加外语信息字段
        languageType: '',
        languageClass: '',
        otherLanguageType: '',
        otherLanguageClass: '',
        languageListening: '',
        languageSpeaking: '',
        languageReading: '',
        languageWriting: '',

        // 工作信息
        employeeId: '',
        department: '',
        subDepartment: '',
        position: '',
        administrativeLevel: '',
        titleLevels: [], // 改为数组支持多选
        supervisor: '',
        // 执业资格相关字段改为数组，支持多个执业资格
        professionalQualifications: [],
        safetyEngineerLevels: [],
        constructorLevels: [],
        constructorSpecialties: [],
        tempConstructorLevel: '', // 临时存储建造师级别
        showTitleLevelsMenu: false, // 控制职称级别下拉菜单显示
        showQualificationMenu: false,
        status: '',
        workType: '',
        firstWorkDate: '',
        entryDate: '',
        probationMonths: '',
        probationEndDate: ''});

        // 添加自动填充相关的状态
    const [showAutoFillModal, setShowAutoFillModal] = useState(false);
    const [autoFillText, setAutoFillText] = useState('');

    // 自动填充功能
    const handleAutoFill = () => {
        setShowAutoFillModal(true);
    };

    // 处理自动填充文本输入
    const handleAutoFillTextChange = (e) => {
        setAutoFillText(e.target.value);
    };

    // 处理自动填充文本提交
    const handleAutoFillSubmit = () => {
        // 解析文本并填充表单
        const employeeData = parseEmployeeText(autoFillText);
        if (employeeData) {
            setFormData(prev => ({
                ...prev,
                ...employeeData
            }));
            setShowAutoFillModal(false);
            setAutoFillText('');
        } else {
            alert('无法识别员工信息，请检查输入文本格式');
        }
    };

            // 解析员工文本信息 - 改进版
            const parseEmployeeText = (text) => {
                if (!text.trim()) return null;

                // 创建一个映射表，将文本中的字段名映射到表单中的字段名
                const fieldMapping = {
                    '姓名': 'name',
                    '性别': 'gender',
                    '血型': 'bloodType',
                    '民族': 'ethnicity',
                    '国籍': 'nationality',
                    '身份证号': 'idNumber',
                    '证件号码': 'idNumber',
                    '证件有效期': 'idValidDate',
                    '出生日期': 'birthDate',
                    '籍贯': 'birthplace',
                    '婚姻状况': 'maritalStatus',
                    '家庭住址': 'address',
                    '联系电话': 'phone',
                    '邮箱': 'email',
                    '常用药物及特别说明': 'medication',
                    '开户行': 'bankName',
                    '银行卡号': 'bankAccount',
                    '紧急联系人姓名': 'emergencyContact.name',
                    '联系人电话': 'emergencyContact.phone',
                    '联系人关系': 'emergencyContact.relationship',
                    '第一学历': 'firstEducation',
                    '第一学历类型': 'firstEducationType',
                    '第一学历毕业时间': 'firstGraduationDate',
                    '最高学历': 'finalEducation',
                    '最高学历类型': 'finalEducationType',
                    '第一学历毕业院校': 'firstSchool',
                    '第一学历院校类型': 'firstSchoolType',
                    '第一学历专业': 'firstMajor',
                    '最高学历毕业院校': 'finalSchool',
                    '最高学历院校类型': 'finalSchoolType',
                    '最高学历专业': 'finalMajor',
                    '最高学历毕业时间': 'finalGraduationDate',
                    '外语类别': 'languageType',
                    '外语等级': 'languageClass',
                    '听力水平': 'languageListening',
                    '口语水平': 'languageSpeaking',
                    '阅读水平': 'languageReading',
                    '写作水平': 'languageWriting',
                    '工号': 'employeeId',
                    '工作性质': 'workType',
                    '在职状态': 'status',
                    '入职日期': 'entryDate',
                    '岗位名称': 'position',
                    '所属部门': 'department',
                    '子部门': 'subDepartment',
                    '主管领导': 'supervisor',
                    '职称级别': 'titleLevel'
                };

                const data = {};
                const educationFields = [];  // 用于存储教育相关字段的顺序

                // 使用更宽松的正则表达式匹配所有键值对
                const pattern = /([^:：,，\n]+)[：:]\s*([^,，\n]+)(?=[,，\n]|$)/g;
                let match;

                while ((match = pattern.exec(text)) !== null) {
                    const key = match[1].trim();
                    const value = match[2].trim();

                    // 记录教育相关字段的顺序
                    if (['学历类型', '毕业时间', '毕业院校', '院校类型', '专业'].includes(key)) {
                        educationFields.push({ key, value, position: match.index });
                    }

                    // 查找映射的字段名
                    const fieldName = fieldMapping[key];
                    if (fieldName) {
                        if (fieldName.includes('.')) {
                            // 处理嵌套字段，如emergencyContact.name
                            const [parent, child] = fieldName.split('.');
                            if (!data[parent]) data[parent] = {};
                            data[parent][child] = value;
                        } else {
                            // 特殊处理血型
                            if (fieldName === 'bloodType') {
                                // 提取血型字母并标准化格式
                                const bloodTypeMatch = value.match(/([ABO]+|AB)/i);
                                if (bloodTypeMatch) {
                                    data[fieldName] = bloodTypeMatch[1].toUpperCase();
                                } else {
                                    data[fieldName] = value;
                                }
                            } else {
                                data[fieldName] = value;
                            }
                        }
                    }
                }

                // 处理教育相关字段
                if (educationFields.length > 0) {
                    // 找到第一学历和最高学历的位置
                    const firstEduIndex = text.indexOf('第一学历');
                    const finalEduIndex = text.indexOf('最高学历');

                    // 如果找到了第一学历和最高学历
                    if (firstEduIndex !== -1 && finalEduIndex !== -1) {
                        // 按照位置排序教育字段
                        educationFields.sort((a, b) => a.position - b.position);

                        // 遍历所有教育字段，根据它们的位置判断属于第一学历还是最高学历
                        educationFields.forEach(field => {
                            // 如果字段位置在最高学历之后，则认为是最高学历的字段
                            if (field.position > finalEduIndex) {
                                switch (field.key) {
                                    case '学历类型':
                                        data.finalEducationType = field.value;
                                        break;
                                    case '毕业时间':
                                        data.finalGraduationDate = field.value;
                                        break;
                                    case '毕业院校':
                                        data.finalSchool = field.value;
                                        break;
                                    case '院校类型':
                                        data.finalSchoolType = field.value;
                                        break;
                                    case '专业':
                                        data.finalMajor = field.value;
                                        break;
                                }
                            }
                            // 否则认为是第一学历的字段
                            else {
                                switch (field.key) {
                                    case '学历类型':
                                        data.firstEducationType = field.value;
                                        break;
                                    case '毕业时间':
                                        data.firstGraduationDate = field.value;
                                        break;
                                    case '毕业院校':
                                        data.firstSchool = field.value;
                                        break;
                                    case '院校类型':
                                        data.firstSchoolType = field.value;
                                        break;
                                    case '专业':
                                        data.firstMajor = field.value;
                                        break;
                                }
                            }
                        });
                    }
                }

                // 特殊处理身份证号码自动提取出生日期
                if (data.idNumber && data.idNumber.length === 18 && !data.birthDate) {
                    const year = data.idNumber.substring(6, 10);
                    const month = data.idNumber.substring(10, 12);
                    const day = data.idNumber.substring(12, 14);
                    data.birthDate = `${year}-${month}-${day}`;
                }

                // 处理证件类型
                if (data.idNumber && data.idNumber.length === 18 && !data.idType) {
                    data.idType = '居民身份证';
                }

                // 处理国籍
                if (data.idType === '居民身份证' && !data.nationality) {
                    data.nationality = '中国';
                }

                // 处理职称级别（支持多个职称，用顿号或逗号分隔）
                if (data.titleLevel) {
                    const titleLevels = [];
                    const professionalQualifications = [];
                    const safetyEngineerLevels = [];
                    const constructorLevels = [];
                    const constructorSpecialties = [];

                    // 分割多个职称
                    const titles = data.titleLevel.split(/[、，,]/);

                    titles.forEach(title => {
                        title = title.trim();
                        if (title.includes('注册安全工程师')) {
                            const match = title.match(/注册安全工程师[（(](.+)[）)]/);
                            if (match) {
                                if (!titleLevels.includes('执业资格')) {
                                    titleLevels.push('执业资格');
                                }
                                professionalQualifications.push('注册安全工程师');
                                safetyEngineerLevels.push(match[1]);
                            }
                        } else if (title.includes('注册建造师')) {
                            const match = title.match(/注册建造师[（(](.+)[）)]/);
                            if (match) {
                                if (!titleLevels.includes('执业资格')) {
                                    titleLevels.push('执业资格');
                                }
                                professionalQualifications.push('注册建造师');
                                const parts = match[1].split(/(?=[一二三])/);
                                if (parts.length > 0) {
                                    constructorLevels.push(parts[0]);
                                    constructorSpecialties.push(parts.length > 1 ? parts.slice(1).join('') : '');
                                }
                            }
                        } else if (['无', '初级', '中级', '高级'].includes(title)) {
                            titleLevels.push(title);
                        }
                    });

                    data.titleLevels = titleLevels;
                    data.professionalQualifications = professionalQualifications;
                    data.safetyEngineerLevels = safetyEngineerLevels;
                    data.constructorLevels = constructorLevels;
                    data.constructorSpecialties = constructorSpecialties;

                    // 删除旧的单个字段
                    delete data.titleLevel;
                }

                // 处理日期格式
                const dateFields = ['birthDate', 'idValidDate', 'entryDate', 'firstGraduationDate', 'finalGraduationDate'];
                dateFields.forEach(field => {
                    if (data[field] && data[field] !== '长期') {
                        // 尝试标准化日期格式为 YYYY-MM-DD
                        const dateMatch = data[field].match(/(\d{4})[年/-]?(\d{1,2})[月/-]?(\d{1,2})[日]?/);
                        if (dateMatch) {
                            const year = dateMatch[1];
                            const month = dateMatch[2].padStart(2, '0');
                            const day = dateMatch[3].padStart(2, '0');
                            data[field] = `${year}-${month}-${day}`;
                        }
                    }
                });

                // 处理学历信息的特殊情况
                // 尝试从文本中直接提取最高学历相关信息
                if (!data.finalEducationType) {
                    const finalEduTypeMatch = text.match(/最高学历类型[：:]\s*([^,，\n]+)/);
                    if (finalEduTypeMatch) {
                        data.finalEducationType = finalEduTypeMatch[1].trim();
                    }
                }

                if (!data.finalSchoolType) {
                    const finalSchoolTypeMatch = text.match(/最高学历院校类型[：:]\s*([^,，\n]+)/);
                    if (finalSchoolTypeMatch) {
                        data.finalSchoolType = finalSchoolTypeMatch[1].trim();
                    }
                }

                if (!data.finalGraduationDate) {
                    const finalGradDateMatch = text.match(/最高学历毕业时间[：:]\s*([^,，\n]+)/);
                    if (finalGradDateMatch) {
                        const gradDate = finalGradDateMatch[1].trim();
                        // 标准化日期格式
                        const dateMatch = gradDate.match(/(\d{4})[年/-]?(\d{1,2})[月/-]?(\d{1,2})[日]?/);
                        if (dateMatch) {
                            const year = dateMatch[1];
                            const month = dateMatch[2].padStart(2, '0');
                            const day = dateMatch[3].padStart(2, '0');
                            data.finalGraduationDate = `${year}-${month}-${day}`;
                        } else {
                            data.finalGraduationDate = gradDate;
                        }
                    }
                }

                // 处理最高学历与第一学历相同的情况
                if (data.finalEducation === '与第一学历相同' ||
                    (data.finalEducation && data.firstEducation &&
                    data.finalEducation === data.firstEducation)) {
                    data.isFinalSameAsFirst = true;
                    data.finalEducation = 'same';
                    data.finalEducationType = data.firstEducationType;
                    data.finalSchool = data.firstSchool;
                    data.finalSchoolType = data.firstSchoolType;
                    data.finalMajor = data.firstMajor;
                    data.finalGraduationDate = data.firstGraduationDate;
                }

            // 在返回数据之前添加标准化处理
            const standardizeValue = (field, value) => {
                if (!value) return value;

                // 处理学历类型
                if (field.includes('EducationType')) {
                    return value.replace(/普通|全日制/, '').trim() || '全日制';
                }

                // 处理院校类型
                if (field.includes('SchoolType')) {
                    if (value.includes('985') || value.includes('211')) {
                        return '985/211';
                    }
                    return value;
                }

                            /// 处理语言技能
                if (field.includes('language') &&
                (field.includes('Listening') ||
                field.includes('Speaking') ||
                field.includes('Reading') ||
                field.includes('Writing'))) {
                // 标准化语言水平
                if (value.includes('一般') || value.includes('较好')) {
                    return '一般';
                }
                if (value.includes('熟练') || value.includes('良好')) {
                    return '熟练';
                }
                if (value.includes('优秀') || value.includes('精通')) {
                    return '精通';
                    }
                    return value;
                }

                return value;
            };

            // 应用标准化处理到所有字段
            Object.entries(data).forEach(([field, value]) => {
                data[field] = standardizeValue(field, value);
            });

                // 如果提取到了姓名，认为解析成功
                if (data.name) {
                    return data;
                }

                return null;
            };

    useEffect(() => {
        const fetchEmployee = async () => {
            if (id) {
                try {
                    const response = await fetch(`${config.apiBaseUrl}/employees/${id}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    if (!response.ok) {
                        throw new Error('获取员工数据失败');
                    }
                    const data = await response.json();
                    // 根据 idValidDate 的值设置 idValidType
                    if (data.idValidDate === '长期') {
                        data.idValidType = 'permanent';
                    } else {
                        data.idValidType = 'temporary';
                    }

                    // 确保新的数组字段存在，如果不存在则设置默认值
                    const processedData = {
                        ...data,
                        titleLevels: data.titleLevels || (data.titleLevel ? [data.titleLevel] : []),
                        professionalQualifications: data.professionalQualifications || (data.professionalQualification ? [data.professionalQualification] : []),
                        safetyEngineerLevels: data.safetyEngineerLevels || (data.safetyEngineerLevel ? [data.safetyEngineerLevel] : []),
                        constructorLevels: data.constructorLevels || (data.constructorLevel ? [data.constructorLevel] : []),
                        constructorSpecialties: data.constructorSpecialties || (data.constructorSpecialty ? [data.constructorSpecialty] : [])
                    };

                    setFormData(processedData);
                } catch (error) {
                    console.error('获取员工数据失败:', error);
                    alert('获取员工数据失败: ' + error.message);
                }
            }
        };
        fetchEmployee();
    }, [id]);

    // 修改提交处理函数
    const handleSubmit = async (e) => {
        if (e && e.preventDefault) {
            e.preventDefault();
        }

        // 返回一个 Promise，以便其他函数可以等待完成
        return new Promise(async (resolve, reject) => {
        try {
            // 使用展开运算符创建新对象，确保emergencyContact结构完整
            const submitData = {
                ...formData,
                emergencyContact: {
                    name: formData.emergencyContact?.name || '',
                    phone: formData.emergencyContact?.phone || '',
                    relationship: formData.emergencyContact?.relationship || ''
                }
            };
            // 添加年龄验证
            if (formData.birthDate) {
                const age = validateAge(formData.birthDate);
                const ageError = checkAgeLimit(age);
                if (ageError) {
                    alert(ageError);
                    return;
                }
            }
            // 添加前端验证
            if (formData.nationality === '中国' || formData.nationality === '中华人民共和国') {
                if (formData.idType === '居民身份证') {
                    const errorMsg = validateIdNumber(formData.idNumber);
                    if (errorMsg) {
                        alert(errorMsg);
                        return;
                    }
                }
            } else {
                // 非中国籍证件号码验证
                if (!formData.idNumber || formData.idNumber.length < 5) {
                    alert('请输入有效的证件号码');
                    return;
                }
            }
            // 验证工号格式 - 允许大小写字母开头
            if (!/^[a-zA-Z]\d{3}$/.test(formData.employeeId)) {
                alert('工号格式无效，应为一个字母后跟三个数字，例如：M001');
                return;
            }
            // 确保工号第一个字母为大写
            const formattedEmployeeId = formData.employeeId.charAt(0).toUpperCase() + formData.employeeId.slice(1);
            submitData.employeeId = formattedEmployeeId;
            // 如果最高学历是"与第一学历相同"，提交前将其转换为实际值
            if (submitData.finalEducation === 'same') {
                submitData.finalEducation = submitData.firstEducation;
            }

            // 处理证件有效期类型
            if (submitData.idValidType === 'permanent') {
                submitData.idValidDate = '长期';
            }
            // 处理试用期问题
            if (submitData.workType !== '试用') {
                submitData.probationMonths = '无';
                submitData.probationEndDate = '';
            } else if (submitData.probationMonths === '无' || !submitData.probationMonths) {
                submitData.probationMonths = '无';
                submitData.probationEndDate = '';
            }
            // 确保部门信息正确
            if (submitData.administrativeLevel === '总经理' || submitData.administrativeLevel === '副总经理') {
                submitData.department = '全公司';
                submitData.subDepartment = '';
            } else if (!submitData.department) {
                alert('请选择部门');
                return;
            }
            const response = await fetch(id ?
                `${config.apiBaseUrl}/employees/${id}` :
                `${config.apiBaseUrl}/employees`,
                {
                    method: id ? 'PUT' : 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                    },
                    body: JSON.stringify(submitData)
                }
            );
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '保存失败');
            }
            // 保存成功，显示成功消息
            console.log('保存成功');
            alert('保存成功！');

            if (!e || !e.target) {
                return;
            }
        } catch (error) {
            console.error('保存失败详细信息:', error);
            let errorMessage = '保存失败: ';
            if (error.message.includes('duplicate key')) {
                if (error.message.includes('employeeId')) {
                    errorMessage += '工号已存在';
                } else if (error.message.includes('idNumber')) {
                    errorMessage += '身份证号已存在';
                } else {
                    errorMessage += '存在重复信息';
                }
            } else if (error.message.includes('match')) {
                errorMessage += '请检查输入格式是否正确';
            } else {
                errorMessage += error.message || '服务器连接失败';
            }
            alert(errorMessage);
        }
        });
    };

    // 在 handleReset 函数中也添加这些字段
    const handleReset = () => {
        console.log('执行表单重置操作');
        setFormData({
            // 个人信息
            name: '',
            gender: '',
            bloodType: '',
            nationality: '',  // 添加国家字段
            ethnicity: '',
            idType: '',      // 添加证件类型字段
            idNumber: '',
            idValidDate: '',
            birthDate: '',
            birthplace: '',
            email: '',
            phone: '',
            maritalStatus: '',
            address: '',
            medication: '',
            bankName: '',     // 开户行
            bankAccount: '',  // 银行卡号
            emergencyContact: {
                name: '',
                phone: '',
                relationship: ''
            },

            // 学历信息
            firstEducation: '',
            firstEducationType: '',
            firstSchool: '',
            firstSchoolType: '',    // 第一学历院校类型
            firstMajor: '',
            firstGraduationDate: '',
            finalEducation: '',
            finalEducationType: '',
            finalSchool: '',
            finalSchoolType: '',    // 最高学历院校类型
            finalMajor: '',
            finalGraduationDate: '',
            isFinalSameAsFirst: false,  // 重置该标记

            // 添加外语信息字段的重置
            languageType: '',
            languageClass: '',
            otherLanguageType: '',
            otherLanguageClass: '',
            languageListening: '',
            languageSpeaking: '',
            languageReading: '',
            languageWriting: '',

            // 工作信息
            employeeId: '',
            department: '',
            subDepartment: '',
            position: '',
            administrativeLevel:'',
            titleLevels: [],
            supervisor: '',
            professionalQualifications: [],
            safetyEngineerLevels: [],
            constructorLevels: [],
            constructorSpecialties: [],
            tempConstructorLevel: '',
            showTitleLevelsMenu: false,
            showQualificationMenu: false,
            status: '',
            firstWorkDate: '',
            entryDate: '',
            probationMonths: '',
            probationEndDate: '',
            calculateSeniority: ''});
    };

    const getFullTitles = () => {
        const titles = [];

        // 添加基础职称（无、初级、中级、高级）
        const basicTitles = formData.titleLevels.filter(level => ['无', '初级', '中级', '高级'].includes(level));

        // 检查是否有执业资格
        const hasQualifications = formData.titleLevels.includes('执业资格') &&
                                 formData.professionalQualifications &&
                                 formData.professionalQualifications.length > 0;

        // 如果只有执业资格而没有基础职称，不显示"无"
        if (basicTitles.includes('无') && hasQualifications) {
            // 有执业资格时，不显示"无"
        } else {
            // 添加基础职称
            titles.push(...basicTitles);
        }

        // 添加执业资格
        if (hasQualifications) {
            formData.professionalQualifications.forEach((qual, index) => {
                if (qual === '注册安全工程师') {
                    const level = formData.safetyEngineerLevels && formData.safetyEngineerLevels[index];
                    if (level) {
                        titles.push(`${qual}（${level}）`);
                    } else {
                        titles.push(qual);
                    }
                } else if (qual === '注册建造师') {
                    const level = formData.constructorLevels && formData.constructorLevels[index];
                    const specialty = formData.constructorSpecialties && formData.constructorSpecialties[index];
                    if (level) {
                        const specialtyText = specialty ? specialty : '';
                        titles.push(`${qual}（${level}${specialtyText}）`);
                    } else {
                        titles.push(qual);
                    }
                } else {
                    titles.push(qual);
                }
            });
        }

        return titles.length > 0 ? titles.join('、') : '-';
    };

    // 根据内容长度返回相应的CSS类
    const getContentSizeClass = () => {
        const content = getFullTitles();
        const contentLength = content.length;

        if (contentLength > 120) {
            return 'extremely-long-content';
        } else if (contentLength > 80) {
            return 'very-long-content';
        } else if (contentLength > 50) {
            return 'long-content';
        }
        return '';
    };

    // 获取显示用的职称文本（可能是简化版）
    const getDisplayTitles = () => {
        const fullTitles = getFullTitles();
        const contentLength = fullTitles.length;

        // 如果内容不是太长，直接返回完整内容
        if (contentLength <= 80) {
            return fullTitles;
        }

        // 如果内容很长，返回完整内容让CSS处理显示
        return fullTitles;
    };

    // 修改表单布局部分
    return (
        <div className="employee-form-container"
        style={{
            maxWidth: '900px',
            margin: '0 auto',
            padding: '20px',
            width: '100%'
        }}>
            <div className="form-header">
                <h2>{id ? '编辑员工' : '添加员工'}</h2>
            </div>

            {showAutoFillModal && (
                <div style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: 1000
                }}>
                    <div style={{
                        backgroundColor: 'white',
                        padding: '20px',
                        borderRadius: '8px',
                        width: '100%',
                        maxWidth: '400px',
                        boxSizing: 'border-box'
                    }}>
                        <h3 style={{ marginTop: 0 }}>粘贴员工信息</h3>
                        <p style={{ color: '#666', fontSize: '14px' }}>
                            请粘贴包含员工信息的文本，系统将自动识别并填充表单。
                            <br />
                            支持格式如：姓名: 张三, 性别: 男, 工号: Z001...
                        </p>
                        <textarea
                            value={autoFillText}
                            onChange={handleAutoFillTextChange}
                            style={{
                                width: '100%',
                                minWidth: 0,
                                height: '200px',
                                padding: '10px',
                                marginBottom: '15px',
                                border: '1px solid #ccc',
                                borderRadius: '4px',
                                resize: 'none',
                                boxSizing: 'border-box'
                            }}
                            placeholder="请粘贴员工信息文本..."
                        />
                        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px', flexWrap: 'wrap' }}>
                            <button
                                type="button"
                                onClick={() => setShowAutoFillModal(false)}
                                style={{
                                    padding: '8px 15px',
                                    border: '1px solid #ccc',
                                    borderRadius: '4px',
                                    backgroundColor: '#f0f0f0',
                                    cursor: 'pointer',
                                    flex: '1 1 120px',
                                    minWidth: 0
                                }}
                            >
                                取消
                            </button>
                            <button
                                type="button"
                                onClick={handleAutoFillSubmit}
                                style={{
                                    padding: '8px 15px',
                                    border: '1px solid #4CAF50',
                                    borderRadius: '4px',
                                    backgroundColor: '#4CAF50',
                                    color: 'white',
                                    cursor: 'pointer',
                                    flex: '1 1 120px',
                                    minWidth: 0
                                }}
                            >
                                确认填充
                            </button>
                        </div>
                    </div>
                </div>
            )}

            <form onSubmit={handleSubmit} className="employee-form">
                {/* 基本信息部分 */}
                <div className="form-section">
                    <h3>基本信息</h3>
                    <div className="form-content">
                        {/* 1. 基本信息行 */}
                        <div className="form-row basic-info" style={{ display: 'flex', width: '100%', gap: '10px' }}>
                        <div className="form-group" style={{ flex: '2' }}>
                                <label>姓名</label>
                                <input type="text" name="name" value={formData.name} onChange={handleChange} />
                            </div>
                         <div className="form-group" style={{ flex: '1' }}>
                            <label>性别</label>
                            <select name="gender" value={formData.gender} onChange={handleChange} >
                                <option value="">请选择</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                        </div>
                        <div className="form-group" style={{ flex: '1' }}>
                            <label>血型</label>
                            <select name="bloodType" value={formData.bloodType} onChange={handleChange} >
                                <option value="">请选择</option>
                                <option value="A">A型</option>
                                <option value="B">B型</option>
                                <option value="O">O型</option>
                                <option value="AB">AB型</option>
                            </select>
                        </div>
                        <div className="form-group" style={{ flex: '1.8' }}>
                            <label>国家</label>
                            <input
                            type="text"
                            name="nationality"
                            value={formData.nationality}
                            onChange={handleChange}
                            placeholder="请输入国家名称"
                            />
                        </div>
                        <div className="form-group" style={{ flex: '1' }}>
                                <label>民族</label>
                                <input type="text" name="ethnicity" value={formData.ethnicity} onChange={handleChange} />
                        </div>
                        </div>

                        <div className="form-row" style={{ display: 'flex', width: '100%', gap: '10px' }}>
                        <div className="form-group" style={{ flex: '1' }}>
                            <label>证件类型</label>
                            <select
                                name="idType"
                                value={formData.idType}
                                onChange={handleChange}
                                disabled={!formData.nationality || formData.nationality === '中国' || formData.nationality === '中华人民共和国'}
                            >
                                <option value="">请选择证件类型</option>
                                {(formData.nationality === '中国' || formData.nationality === '中华人民共和国') ? (
                                    <option value="居民身份证">居民身份证</option>
                                ) : formData.nationality ? (
                                    <>
                                        <option value="护照">护照</option>
                                        <option value="港澳通行证">港澳通行证</option>
                                        <option value="台湾通行证">台湾通行证</option>
                                    </>
                                ) : null}
                            </select>
                        </div>
                        <div className="form-group" style={{ flex: '1' }}>
                            <label>{formData.nationality === '中国' || formData.nationality === '中华人民共和国' ? '身份证号码' : '证件号码'}</label>
                            <input type="text" name="idNumber" value={formData.idNumber} onChange={handleChange} />
                        </div>
                        <div className="form-group" style={{ flex: '1', position: 'relative' }}>
                            <label>证件有效期</label>
                            <div style={{ position: 'relative' }}>
                                <div style={{ display: 'flex' }}>
                                    <select
                                        value={formData.idValidType || 'temporary'}
                                        onChange={(e) => {
                                            const newType = e.target.value;
                                            setFormData(prev => ({
                                                ...prev,
                                                idValidType: newType,
                                                idValidDate: newType === 'permanent' ? '长期' : (prev.idValidDate === '长期' ? '' : prev.idValidDate)
                                            }));
                                        }}
                                        style={{
                                            width: '60px',
                                            marginRight: '5px',
                                            height: '32px',
                                            border: '1px solid #ccc',
                                            borderRadius: '4px'
                                        }}
                                    >
                                        <option value="temporary">定期</option>
                                        <option value="permanent">长期</option>
                                    </select>
                                    <div style={{ flex: 1, position: 'relative' }}>
                                        {formData.idValidType === 'permanent' ? (
                                            <input
                                                type="text"
                                                value="长期"
                                                readOnly
                                                className="form-control"
                                            />
                                        ) : (
                                            <DatePicker
                                                selected={parseDate(formData.idValidDate)}
                                                onChange={date => {
                                                    setFormData(prev => ({
                                                        ...prev,
                                                        idValidDate: date ? date.toISOString().slice(0, 10) : ''
                                                    }));
                                                }}
                                                dateFormat="yyyy-MM-dd"
                                                placeholderText="请选择日期"
                                                className="form-control"
                                                shouldCloseOnSelect={true}
                                                showPopperArrow={false}
                                                onClickOutside={() => {}}
                                                onChangeRaw={(e) => {
                                                    const value = e.target.value;
                                                    if (value) {
                                                        handleDateInputMatch(value, 'idValidDate');
                                                    }
                                                }}
                                                popperProps={{
                                                    positionFixed: true,
                                                    modifiers: {
                                                        preventOverflow: {
                                                            enabled: true,
                                                            escapeWithReference: false,
                                                            boundariesElement: 'viewport'
                                                        },
                                                        offset: {
                                                            enabled: true,
                                                            offset: '0, 45'
                                                        }
                                                    }
                                                }}
                                            />
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="form-group" style={{ flex: '0.6' }}>
                            <label>出生日期</label>
                            <DatePicker
                                selected={parseDate(formData.birthDate)}
                                onChange={date => handleChange({ target: { name: 'birthDate', value: date ? date.toISOString().slice(0, 10) : '' } })}
                                dateFormat="yyyy-MM-dd"
                                placeholderText="请选择日期"
                                disabled={formData.idType === '居民身份证'}
                                className="form-control"
                                shouldCloseOnSelect={true}
                                showPopperArrow={false}
                                onClickOutside={() => {}}
                                onChangeRaw={(e) => {
                                    if (formData.idType === '居民身份证') return;
                                    const value = e.target.value;
                                    if (value) {
                                        handleDateInputMatch(value, 'birthDate');
                                    }
                                }}
                                popperProps={{
                                    positionFixed: true,
                                    modifiers: {
                                        preventOverflow: {
                                            enabled: true,
                                            escapeWithReference: false,
                                            boundariesElement: 'viewport'
                                        },
                                        offset: {
                                            enabled: true,
                                            offset: '0, 45'
                                        }
                                    }
                                }}
                            />
                        </div>
                            <div className="form-group" style={{ flex: '1' }}>
                                <label>籍贯</label>
                                <input type="text" name="birthplace" value={formData.birthplace} onChange={handleChange}/>
                            </div>
                        </div>

                        {/* 3. 家庭住址、联系电话和邮箱行 */}
                        <div className="form-row" style={{ display: 'flex', gap: '10px', width: '100%' }}>
                            <div className="form-group" style={{ flex: '0.6' }}>
                                <label>婚姻状况</label>
                                <select name="maritalStatus" value={formData.maritalStatus} onChange={handleChange}>
                                    <option value="">请选择</option>
                                    <option value="未婚">未婚</option>
                                    <option value="已婚">已婚</option>
                                    <option value="离异">离异</option>
                                </select>
                            </div>
                            <div className="form-group" style={{ flex: '2.3' }}>
                                <label>家庭住址</label>
                                <input type="text" name="address" value={formData.address} onChange={handleChange}/>
                            </div>
                            <div className="form-group" style={{ flex: '1' }}>
                                <label>联系电话</label>
                                <input type="tel" name="phone" value={formData.phone} onChange={handleChange}/>
                            </div>
                            <div className="form-group" style={{ flex: '1.2' }}>
                                <label>邮箱</label>
                                <input type="email" name="email" value={formData.email} onChange={handleChange}/>
                            </div>
                        </div>

                        {/* 4. 常用药物行 */}
                        <div className="form-group full-width">
                            <label>常用药物及特别说明</label>
                            <textarea
                                name="medication"
                                value={formData.medication}
                                onChange={handleChange}
                                placeholder='如无，请填写"无"'
                                style={{
                                    minHeight: '60px',
                                    resize: 'vertical',
                                    overflow: 'hidden',
                                    height: 'auto'
                                }}
                                onInput={(e) => {
                                    e.target.style.height = 'auto';
                                    e.target.style.height = e.target.scrollHeight + 'px';
                                }}
                            />
                        </div>

                       {/* 5. 银行卡和紧急联系人信息行 */}
                       <div className="form-row" style={{ display: 'flex', gap: '10px', width: '100%' }}>
                            <div className="form-group" style={{ flex: '1.4' }}>
                                <label>开户行</label>
                                <input
                                    type="text"
                                    name="bankName"
                                    value={formData.bankName}
                                    onChange={handleChange}
                                    placeholder="请输入开户行名称"
                                />
                            </div>
                            <div className="form-group" style={{ flex: '1.3' }}>
                                <label>银行卡号</label>
                                <input
                                    type="text"
                                    name="bankAccount"
                                    value={formData.bankAccount}
                                    onChange={handleChange}
                                    placeholder="请输入银行卡号"
                                />
                            </div>
                            <div className="form-group" style={{ flex: '0.9' }}>
                                <label>紧急联系人姓名</label>
                                <input
                                    type="text"
                                    name="emergencyContact.name"
                                    value={formData.emergencyContact.name}
                                    onChange={handleChange}
                                />
                            </div>
                            <div className="form-group" style={{ flex: '1' }}>
                                <label>联系人电话</label>
                                <input
                                    type="tel"
                                    name="emergencyContact.phone"
                                    value={formData.emergencyContact.phone}
                                    onChange={handleChange}
                                />
                            </div>
                            <div className="form-group" style={{ flex: '0.6' }}>
                                <label>联系人关系</label>
                                <input
                                    type="text"
                                    name="emergencyContact.relationship"
                                    value={formData.emergencyContact.relationship}
                                    onChange={handleChange}
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div className="form-section">
                    <h3>学历信息</h3>
                    <div className="form-content">
                        {/* 第一学历信息行 */}
                        <div className="form-row education-info">
                            <div style={{ display: 'flex', gap: '10px', width: '100%'  }}>
                                <div className="form-group" style={{ flex: '1' }}>
                                    <label>第一学历</label>
                                    <select name="firstEducation" value={formData.firstEducation} onChange={handleChange}>
                                        <option value="">请选择学历</option>
                                        <option value="专科">专科</option>
                                        <option value="本科">本科</option>
                                        <option value="硕士">硕士</option>
                                        <option value="博士">博士</option>
                                    </select>
                                </div>
                                <div className="form-group" style={{ flex: '1' }}>
                                    <label>学历类型</label>
                                    <select name="firstEducationType" value={formData.firstEducationType} onChange={handleChange}>
                                        <option value="">请选择类型</option>
                                        <option value="全日制">全日制</option>
                                        <option value="非全日制">非全日制</option>
                                    </select>
                                </div>
                                <div className="form-group" style={{ flex: '1.5' }}>
                                    <label>毕业时间</label>
                                    <DatePicker
                                        selected={parseDate(formData.firstGraduationDate)}
                                        onChange={date => handleChange({ target: { name: 'firstGraduationDate', value: date ? date.toISOString().slice(0, 10) : '' } })}
                                        dateFormat="yyyy-MM-dd"
                                        placeholderText="请选择日期"
                                        className="form-control"
                                        shouldCloseOnSelect={true}
                                        showPopperArrow={false}
                                        onClickOutside={() => {}}
                                        onChangeRaw={(e) => {
                                            const value = e.target.value;
                                            if (value) {
                                                handleDateInputMatch(value, 'firstGraduationDate');
                                            }
                                        }}
                                        popperProps={{
                                            positionFixed: true,
                                            modifiers: {
                                                preventOverflow: {
                                                    enabled: true,
                                                    escapeWithReference: false,
                                                    boundariesElement: 'viewport'
                                                },
                                                offset: {
                                                    enabled: true,
                                                    offset: '0, 45'
                                                }
                                            }
                                        }}
                                    />
                                </div>
                            </div>
                            <div style={{
                                display: 'flex',
                                gap: '10px',
                                width: '100%',
                                flexWrap: 'nowrap',
                                justifyContent: 'space-between'
                            }}>
                                <div className="form-group" style={{ flex: '1' }}>
                                    <label>最高学历</label>
                                    <select name="finalEducation" value={formData.finalEducation} onChange={handleChange}>
                                        <option value="">请选择学历</option>
                                        <option value="same">与第一学历相同</option>
                                        <option value="专科">专科</option>
                                        <option value="本科">本科</option>
                                        <option value="硕士">硕士</option>
                                        <option value="博士">博士</option>
                                    </select>
                                </div>
                                <div className="form-group" style={{ flex: '1' }}>
                                    <label>学历类型</label>
                                    <select name="finalEducationType" value={formData.finalEducationType} onChange={handleChange} disabled={formData.finalEducation === 'same'}>
                                        <option value="">请选择类型</option>
                                        <option value="全日制">全日制</option>
                                        <option value="非全日制">非全日制</option>
                                    </select>
                                </div>
                                <div className="form-group" style={{ flex: '1.5' }}>
                                    <label>毕业时间</label>
                                    <DatePicker
                                        selected={parseDate(formData.finalGraduationDate)}
                                        onChange={date => handleChange({ target: { name: 'finalGraduationDate', value: date ? date.toISOString().slice(0, 10) : '' } })}
                                        dateFormat="yyyy-MM-dd"
                                        placeholderText="请选择日期"
                                        disabled={formData.finalEducation === 'same'}
                                        className="form-control"
                                        shouldCloseOnSelect={true}
                                        showPopperArrow={false}
                                        onClickOutside={() => {}}
                                        onChangeRaw={(e) => {
                                            if (formData.finalEducation === 'same') return;
                                            const value = e.target.value;
                                            if (value) {
                                                handleDateInputMatch(value, 'finalGraduationDate');
                                            }
                                        }}
                                        popperProps={{
                                            positionFixed: true,
                                            modifiers: {
                                                preventOverflow: {
                                                    enabled: true,
                                                    escapeWithReference: false,
                                                    boundariesElement: 'viewport'
                                                },
                                                offset: {
                                                    enabled: true,
                                                    offset: '0, 45'
                                                }
                                            }
                                        }}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* 学历院校专业行 */}
                        <div className="form-row" style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                            {/* 第一学历信息行 */}
                            <div style={{ display: 'flex', gap: '10px' }}>
                                <div className="form-group" style={{ flex: 2 }}>
                                    <label>第一学历毕业院校</label>
                                    <input type="text" name="firstSchool" value={formData.firstSchool} onChange={handleChange}/>
                                </div>
                                <div className="form-group" style={{ flex: 1 }}>
                                    <label>院校类型</label>
                                    <select name="firstSchoolType" value={formData.firstSchoolType} onChange={handleChange}>
                                        <option value="">请选择</option>
                                        <option value="985/211">985/211院校</option>
                                        <option value="普通">普通院校</option>
                                    </select>
                                </div>
                                <div className="form-group" style={{ flex: 2 }}>
                                    <label>第一学历专业</label>
                                    <input type="text" name="firstMajor" value={formData.firstMajor} onChange={handleChange}/>
                                </div>
                            </div>

                            {/* 最高学历信息行 */}
                            <div style={{ display: 'flex', gap: '10px' }}>
                                <div className="form-group" style={{ flex: 2 }}>
                                    <label>最高学历毕业院校</label>
                                <input type="text" name="finalSchool" value={formData.finalSchool} onChange={handleChange} disabled={formData.finalEducation === 'same'}/>
                                </div>
                                <div className="form-group" style={{ flex: 1 }}>
                                    <label>院校类型</label>
                                    <select name="finalSchoolType" value={formData.finalSchoolType} onChange={handleChange} disabled={formData.finalEducation === 'same'}>
                                        <option value="">请选择</option>
                                        <option value="985/211">985/211院校</option>
                                        <option value="普通">普通院校</option>
                                    </select>
                                </div>
                                <div className="form-group" style={{ flex: 2 }}>
                                    <label>最高学历专业</label>
                                    <input type="text" name="finalMajor" value={formData.finalMajor} onChange={handleChange} disabled={formData.finalEducation === 'same'}/>
                                </div>
                            </div>
                        </div>
                        {/* 外语信息行 */}
                        <div className="form-row language-info" style={{ display: 'flex', gap: '10px', width: '100%', flexWrap: 'nowrap' }}>
                            <div className="form-group" style={{ flex: '0 0 200px' }}>
                                <label>外语类别</label>
                                <select name="languageType" value={formData.languageType} onChange={handleChange}>
                                    <option value="">请选择外语类别</option>
                                    <option value="英语">英语</option>
                                    <option value="其他">其他</option>
                                    <option value="无">无</option>
                                </select>
                            </div>

                            {formData.languageType === '其他' ? (
                                <>
                                    <div className="form-group" style={{ flex: '1 1 200px' }}>
                                        <label>其他外语名称</label>
                                        <input
                                            type="text"
                                            name="otherLanguageType"
                                            value={formData.otherLanguageType}
                                            onChange={handleChange}
                                        />
                                    </div>
                                    <div className="form-group" style={{ flex: '1 1 150px' }}>
                                        <label>外语等级</label>
                                        <input
                                            type="text"
                                            name="otherLanguageClass"
                                            value={formData.otherLanguageClass}
                                            onChange={handleChange}
                                        />
                                    </div>
                                </>
                            ) : formData.languageType === '英语' && (
                                <div className="form-group" style={{ flex: '1 1 200px' }}>
                                    <label>外语等级</label>
                                    <select
                                        name="languageClass"
                                        value={formData.languageClass}
                                        onChange={handleChange}
                                        disabled={formData.languageType === '无'}
                                    >
                                        <option value="无">无</option>
                                        <option value="">请选择等级</option>
                                        <option value="CET-4">CET-4</option>
                                        <option value="CET-6">CET-6</option>
                                        <option value="TEM-4">TEM-4</option>
                                        <option value="TEM-8">TEM-8</option>
                                        <option value="IELTS">IELTS</option>
                                        <option value="TOEFL">TOEFL</option>
                                        <option value="专业四级">专业四级</option>
                                        <option value="专业八级">专业八级</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                            )}
                            {/* 移除条件渲染，让以下选项始终显示 */}
                            <div className="form-group" style={{ flex: '1 1 150px' }}>
                                <label>听力水平</label>
                                <select name="languageListening" value={formData.languageListening} onChange={handleChange}
                                disabled={formData.languageType === '无'}>
                                    <option value="">请选择</option>
                                    <option value="无">无</option>
                                    <option value="基础">基础</option>
                                    <option value="熟练">熟练</option>
                                    <option value="精通">精通</option>
                                </select>
                            </div>
                            <div className="form-group" style={{ flex: '1 1 150px' }}>
                                <label>口语水平</label>
                                <select name="languageSpeaking" value={formData.languageSpeaking} onChange={handleChange}
                                disabled={formData.languageType === '无'}>
                                    <option value="">请选择</option>
                                    <option value="无">无</option>
                                    <option value="基础">基础</option>
                                    <option value="熟练">熟练</option>
                                    <option value="精通">精通</option>
                                </select>
                            </div>
                            <div className="form-group" style={{ flex: '1 1 150px' }}>
                                <label>阅读水平</label>
                                <select name="languageReading" value={formData.languageReading} onChange={handleChange}
                                disabled={formData.languageType === '无'}>
                                    <option value="">请选择</option>
                                    <option value="无">无</option>
                                    <option value="基础">基础</option>
                                    <option value="熟练">熟练</option>
                                    <option value="精通">精通</option>
                                </select>
                            </div>
                            <div className="form-group" style={{ flex: '1 1 150px' }}>
                                <label>写作水平</label>
                                <select name="languageWriting" value={formData.languageWriting} onChange={handleChange}
                                disabled={formData.languageType === '无'}>
                                    <option value="">请选择</option>
                                    <option value="无">无</option>
                                    <option value="基础">基础</option>
                                    <option value="熟练">熟练</option>
                                    <option value="精通">精通</option>
                                </select>
                            </div>
                        </div>
                        {/* 后续内容保持不变 */}
                    </div>
                </div>

                <div className="form-section">
                    <h3>工作信息</h3>
                    <div className="form-content">
                        {/* 第一行：工号，工作性质，在职状态，入职时间，试用期，试用期截止日期 */}
                        <div className="form-row" style={{ display: 'flex', gap: '10px', width: '100%' }}>
                            <div className="form-group" style={{ flex: '0.8' }}>
                                <label>工号</label>
                                <input type="text" name="employeeId" value={formData.employeeId} onChange={handleChange} />
                            </div>
                            <div className="form-group" style={{ flex: '1' }}>
                                <label>工作性质</label>
                                <select name="workType" value={formData.workType} onChange={handleChange}>
                                    <option value="">请选择性质</option>
                                    <option value="试用">试用</option>
                                    <option value="兼职">兼职</option>
                                    <option value="全职">全职</option>
                                </select>
                            </div>
                            <div className="form-group" style={{ flex: '1' }}>
                                <label>在职状态</label>
                                <select name="status" value={formData.status} onChange={handleChange}>
                                    <option value="">请选择状态</option>
                                    <option value="在职">在职</option>
                                    <option value="离职">离职</option>
                                </select>
                            </div>
                            <div className="form-group" style={{ flex: '1.3' }}>
                                <label>入职日期</label>
                                <DatePicker
                                    selected={parseDate(formData.entryDate)}
                                    onChange={date => handleChange({ target: { name: 'entryDate', value: date ? date.toISOString().slice(0, 10) : '' } })}
                                    dateFormat="yyyy-MM-dd"
                                    placeholderText="请选择日期"
                                    className="form-control"
                                    shouldCloseOnSelect={true}
                                    showPopperArrow={false}
                                    onClickOutside={() => {}}
                                    onChangeRaw={(e) => {
                                        const value = e.target.value;
                                        if (value) {
                                            handleDateInputMatch(value, 'entryDate');
                                        }
                                    }}
                                    popperProps={{
                                        positionFixed: true,
                                        modifiers: {
                                            preventOverflow: {
                                                enabled: true,
                                                escapeWithReference: false,
                                                boundariesElement: 'viewport'
                                            },
                                            offset: {
                                                enabled: true,
                                                offset: '0, 45'
                                            }
                                        }
                                    }}
                                />
                            </div>
                            <div className="form-group" style={{ flex: '1' }}>
                                <label>试用期（月）</label>
                                <select
                                    name="probationMonths"
                                    value={formData.workType === '试用' ? formData.probationMonths : '无'}
                                    onChange={handleChange}
                                    disabled={formData.workType !== '试用'}
                                >
                                    <option value="无">无</option>
                                    <option value="1">1个月</option>
                                    <option value="2">2个月</option>
                                    <option value="3">3个月</option>
                                    <option value="6">6个月</option>
                                </select>
                            </div>
                            <div className="form-group" style={{ flex: '1.2' }}>
                                <label>试用期截止日期</label>
                                <DatePicker
                                    selected={parseDate(formData.probationEndDate)}
                                    onChange={date => handleChange({ target: { name: 'probationEndDate', value: date ? date.toISOString().slice(0, 10) : '' } })}
                                    dateFormat="yyyy-MM-dd"
                                    placeholderText="请选择日期"
                                    disabled
                                    className="form-control readonly-input"
                                    shouldCloseOnSelect={true}
                                    showPopperArrow={false}
                                    onClickOutside={() => {}}
                                    onChangeRaw={(e) => {
                                        // 由于该字段是禁用的，这个处理函数不会被触发
                                        // 但为了保持一致性，我们仍然添加它
                                        if (formData.disabled) return;
                                        const value = e.target.value;
                                        if (value) {
                                            handleDateInputMatch(value, 'probationEndDate');
                                        }
                                    }}
                                    popperProps={{
                                        positionFixed: true,
                                        modifiers: {
                                            preventOverflow: {
                                                enabled: true,
                                                escapeWithReference: false,
                                                boundariesElement: 'viewport'
                                            },
                                            offset: {
                                                enabled: true,
                                                offset: '0, 45'
                                            }
                                        }
                                    }}
                                />
                            </div>
                            <div className="form-group" style={{ flex: '0.9' }}>
                        <label>司龄</label>
                        <input
                            type="text"
                            name="seniority"
                            value={formData.seniority || calculateSeniority(formData.entryDate, formData.workType, formData.probationEndDate)}
                            readOnly
                            className="readonly-input"
                        />
                    </div>
                </div>
                {/* 第二行：岗位名称，管理岗位级别，所属部门，职称级别 */}
                <div className="form-row" style={{ display: 'flex', gap: '10px', width: '100%' }}>
                    <div className="form-group" style={{ flex: '1.3' }}>
                        <label>岗位名称</label>
                        <select name="position" value={formData.position} onChange={handleChange}>
                            <option value="">请选择岗位名称</option>
                            <option value="行政管理">行政管理</option>
                            <option value="技术工程师">技术工程师</option>
                            <option value="项目经理">项目经理</option>
                            <option value="项目代表">项目代表</option>
                            <option value="项目助理">项目助理</option>
                            <option value="项目管理">项目管理</option>
                            <option value="业务经理">业务经理</option>
                            <option value="品控工程师">品控工程师</option>
                            <option value="采购工程师">采购工程师</option>
                            <option value="采购助理">采购助理</option>
                            <option value="外贸专员">外贸专员</option>
                            <option value="出纳/会计">出纳/会计</option>
                            <option value="行政助理">行政助理</option>
                        </select>
                    </div>
                    <div className="form-group" style={{ flex: '1.3' }}>
                        <label>管理岗位级别</label>
                        <select
                            name="administrativeLevel"
                            value={formData.position === '' ? '' : (formData.position === '行政管理' ? formData.administrativeLevel : '无')}
                            onChange={handleChange}
                            disabled={formData.position !== '行政管理'}
                        >
                            {formData.position === '' ? (
                                <option value="">请选择岗位级别</option>
                            ) : formData.position !== '行政管理' ? (
                                <option value="无">无</option>
                            ) : (
                                <>
                                    <option value="">请选择岗位级别</option>
                                    <option value="总经理">总经理</option>
                                    <option value="副总经理">副总经理</option>
                                    <option value="总监/总工">总监/总工</option>
                                    <option value="副总监/副总工">副总监/副总工</option>
                                    <option value="部门经理">部门经理</option>
                                    <option value="部门副经理">部门副经理</option>
                                </>
                            )}
                        </select>
                    </div>

                    <div className="form-group" style={{ flex: '1.5' }}>
                    <label>所属部门</label>
                        <div className="department-select">
                            <div className="custom-select"
                                onClick={() => {
                                    if (formData.administrativeLevel === '总经理' ||
                                        formData.administrativeLevel === '副总经理') {
                                        return;
                                    }
                                    setFormData(prev => ({
                                        ...prev,
                                        showDepartmentSelect: !prev.showDepartmentSelect
                                    }))
                                }}
                                style={{
                                    opacity: formData.administrativeLevel === '总经理' ||
                                            formData.administrativeLevel === '副总经理' ? 0.5 : 1,
                                    cursor: formData.administrativeLevel === '总经理' ||
                                        formData.administrativeLevel === '副总经理' ? 'not-allowed' : 'pointer'
                                }}
                            >
                            {formData.administrativeLevel === '总经理' || formData.administrativeLevel === '副总经理' ?
                                    '全公司' :
                                    (formData.department ?
                                        (formData.subDepartment ? `${formData.department} / ${formData.subDepartment}` : formData.department) :
                                        '请选择部门')}
                            </div>

                            {formData.showDepartmentSelect && (
                                <div className="department-menu">
                                    {departments.map(dept => (
                                        <div
                                            key={dept.value}
                                            className="department-option"
                                            onClick={(e) => {
                                                // 如果点击的是主部门且没有子部门，直接选择主部门
                                                // 如果有子部门，只有当点击的不是子菜单区域时才选择主部门
                                                if (!dept.subOptions) {
                                                    handleDepartmentSelect(dept);
                                                } else if (!e.target.closest('.sub-departments')) {
                                                    handleDepartmentSelect(dept);
                                                }
                                            }}
                                            onMouseEnter={(e) => {
                                                const subMenu = e.currentTarget.querySelector('.sub-departments');
                                                if (subMenu) {
                                                    subMenu.style.display = 'block';
                                                }
                                            }}
                                            onMouseLeave={(e) => {
                                                const subMenu = e.currentTarget.querySelector('.sub-departments');
                                                if (subMenu) {
                                                    subMenu.style.display = 'none';
                                                }
                                            }}
                                        >
                                            {dept.label}
                                            {dept.subOptions && (
                                                <div className="sub-departments">
                                                    {dept.subOptions.map(subDept => (
                                                        <div
                                                            key={subDept.value}
                                                            className="department-option"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleDepartmentSelect(dept, subDept);
                                                            }}
                                                        >
                                                            {subDept.label}
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            )}
                            <input
                                type="hidden"
                                name="department"
                                value={formData.department}
                            />
                        </div>
                    </div>
                    {/* 添加主管领导字段 */}
                    <div className="form-group" style={{ flex: '1.1' }}>
                        <label>主管领导</label>
                        <input
                            type="text"
                            name="supervisor"
                            value={formData.supervisor || ''}
                            onChange={handleChange}
                            placeholder="请输入主管姓名"
                        />
                    </div>

                    {/* 职称级别下拉选择 */}
                    <div className="form-group" style={{ flex: '1.8' }}>
                        <label>职称级别</label>
                        <div className="title-levels-dropdown">
                            <div
                                className="title-levels-selector"
                                onClick={() => {
                                    setFormData(prev => ({
                                        ...prev,
                                        showTitleLevelsMenu: !prev.showTitleLevelsMenu,
                                        showQualificationMenu: false
                                    }));
                                }}
                            >
                                <div className="selected-titles-display">
                                    {formData.titleLevels.length === 0 ? (
                                        <span className="placeholder">请选择职称级别</span>
                                    ) : (
                                        <span
                                            className={`selected-text ${getContentSizeClass()}`}
                                            title={getFullTitles()}
                                        >
                                            {getDisplayTitles()}
                                        </span>
                                    )}
                                </div>
                                <div className="dropdown-arrow">▼</div>
                            </div>

                            {formData.showTitleLevelsMenu && (
                                <div className="title-levels-menu">
                                    {['无', '初级', '中级', '高级', '执业资格'].map(level => (
                                        <div
                                            key={level}
                                            className={`title-level-option ${formData.titleLevels.includes(level) ? 'selected' : ''}`}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                if (level === '执业资格') {
                                                    // 执业资格的处理逻辑
                                                    if (formData.titleLevels.includes(level)) {
                                                        // 如果已经选择了执业资格，继续显示执业资格菜单让用户添加更多资格
                                                        setFormData(prev => ({
                                                            ...prev,
                                                            showTitleLevelsMenu: false,
                                                            showQualificationMenu: true
                                                        }));
                                                    } else {
                                                        // 首次选择执业资格，添加到列表并显示执业资格菜单
                                                        setFormData(prev => ({
                                                            ...prev,
                                                            titleLevels: [...prev.titleLevels, level],
                                                            showTitleLevelsMenu: false,
                                                            showQualificationMenu: true
                                                        }));
                                                    }
                                                } else {
                                                    // 基础职称级别的选择逻辑（无、初级、中级、高级只能单选）
                                                    const basicLevels = ['无', '初级', '中级', '高级'];

                                                    if (level === '无') {
                                                        // 选择"无"时，清空所有职称级别
                                                        setFormData(prev => ({
                                                            ...prev,
                                                            titleLevels: ['无'],
                                                            professionalQualifications: [],
                                                            safetyEngineerLevels: [],
                                                            constructorLevels: [],
                                                            constructorSpecialties: [],
                                                            showTitleLevelsMenu: false
                                                        }));
                                                    } else if (basicLevels.includes(level)) {
                                                        // 基础职称（初级、中级、高级）单选逻辑
                                                        setFormData(prev => {
                                                            const nonBasicLevels = prev.titleLevels.filter(t => !basicLevels.includes(t));

                                                            if (prev.titleLevels.includes(level)) {
                                                                // 再次点击已选择的基础职称，取消选择
                                                                return {
                                                                    ...prev,
                                                                    titleLevels: nonBasicLevels
                                                                };
                                                            } else {
                                                                // 选择新的基础职称，保留执业资格，移除其他基础职称
                                                                return {
                                                                    ...prev,
                                                                    titleLevels: [...nonBasicLevels, level]
                                                                };
                                                            }
                                                        });
                                                    }
                                                }
                                            }}
                                        >
                                            <span className={`option-indicator ${level === '执业资格' ? 'checkbox' : 'radio'}`}>
                                                {formData.titleLevels.includes(level) ? (level === '执业资格' ? '✓' : '●') : ''}
                                            </span>
                                            <span className="option-text">{level}</span>
                                        </div>
                                    ))}
                                </div>
                            )}
                            {formData.showQualificationMenu && (
                                <div
                                    className="qualification-menu"
                                    onClick={(e) => e.stopPropagation()}
                                >
                                    {/* 显示已选择的执业资格 */}
                                    {formData.professionalQualifications.length > 0 && (
                                        <div className="selected-qualifications">
                                            <div className="selected-qualifications-title">已选择的执业资格：</div>
                                            {formData.professionalQualifications.map((qual, index) => (
                                                <div key={index} className="selected-qualification-item">
                                                    <span className="qualification-text">
                                                        {qual === '注册安全工程师' && formData.safetyEngineerLevels[index]
                                                            ? `${qual}（${formData.safetyEngineerLevels[index]}）`
                                                            : qual === '注册建造师' && formData.constructorLevels[index]
                                                            ? `${qual}（${formData.constructorLevels[index]}${formData.constructorSpecialties[index] || ''}）`
                                                            : qual
                                                        }
                                                    </span>
                                                    <span
                                                        className="remove-qualification"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            setFormData(prev => ({
                                                                ...prev,
                                                                professionalQualifications: prev.professionalQualifications.filter((_, i) => i !== index),
                                                                safetyEngineerLevels: prev.safetyEngineerLevels.filter((_, i) => i !== index),
                                                                constructorLevels: prev.constructorLevels.filter((_, i) => i !== index),
                                                                constructorSpecialties: prev.constructorSpecialties.filter((_, i) => i !== index),
                                                                // 如果没有执业资格了，从titleLevels中移除
                                                                titleLevels: prev.professionalQualifications.length === 1
                                                                    ? prev.titleLevels.filter(t => t !== '执业资格')
                                                                    : prev.titleLevels
                                                            }));
                                                        }}
                                                    >
                                                        ×
                                                    </span>
                                                </div>
                                            ))}
                                            <div className="qualification-separator"></div>
                                        </div>
                                    )}

                                    {/* 添加新的执业资格选项 */}
                                    <div className="add-qualification-title">添加执业资格：</div>
                                    {qualificationOptions.map(qual => (
                                        <div
                                            key={qual.value}
                                            className="qualification-option"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                if (qual.value === '注册安全工程师' || qual.value === '注册建造师') {
                                                    // 这些需要进一步选择级别，不做处理
                                                } else {
                                                    // 直接添加执业资格，检查是否已存在
                                                    setFormData(prev => {
                                                        // 检查是否已经选择了这个资格
                                                        if (prev.professionalQualifications.includes(qual.value)) {
                                                            return prev; // 如果已存在，不做任何操作
                                                        }

                                                        // 当添加执业资格时，如果当前只有"无"，应该移除"无"
                                                        let newTitleLevels;
                                                        if (prev.titleLevels.includes('执业资格')) {
                                                            newTitleLevels = prev.titleLevels;
                                                        } else {
                                                            // 如果当前只有"无"，移除"无"并添加"执业资格"
                                                            if (prev.titleLevels.length === 1 && prev.titleLevels.includes('无')) {
                                                                newTitleLevels = ['执业资格'];
                                                            } else {
                                                                newTitleLevels = [...prev.titleLevels, '执业资格'];
                                                            }
                                                        }
                                                        return {
                                                            ...prev,
                                                            titleLevels: newTitleLevels,
                                                            professionalQualifications: [...prev.professionalQualifications, qual.value],
                                                            showQualificationMenu: false
                                                        };
                                                    });
                                                }
                                            }}
                                        >
                                            {qual.label}
                                            {qual.subOptions && (
                                                <div className="qualification-submenu">
                                                    {qual.subOptions.map(subOpt => (
                                                        <div
                                                            key={subOpt.value}
                                                            className="option-item"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                if (subOpt.specialties) {
                                                                    // 这是建造师级别，需要进一步选择专业
                                                                    // 暂时存储级别信息，等待专业选择
                                                                    setFormData(prev => ({
                                                                        ...prev,
                                                                        tempConstructorLevel: subOpt.value
                                                                    }));
                                                                } else {
                                                                    handleQualificationSelect(qual.value, subOpt);
                                                                }
                                                            }}
                                                        >
                                                            {subOpt.label}
                                                            {subOpt.specialties && (
                                                                <div className="qualification-submenu">
                                                                    {subOpt.specialties.map(spec => (
                                                                        <div
                                                                            key={spec.value}
                                                                            className="option-item"
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                handleQualificationSelect(qual.value, subOpt, spec);
                                                                            }}
                                                                        >
                                                                            {spec.label}
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
                {/* 表单按钮部分 */}
                <div className="form-actions">
                    <button type="button" onClick={() => navigate('/employeeList')} className="back-button">
                        返回
                    </button>
                    <button type="button" onClick={handleReset} className="reset-button">
                        重置
                    </button>
                    {!id && (
                        <button
                            type="button"
                            onClick={handleAutoFill}
                            className="auto-fill-button"
                        >
                            自动填充
                        </button>
                    )}
                    <button type="submit" className="submit-button">
                        {id ? '保存修改' : '保存'}
                    </button>

                    {!id && (
                        <button
                            type="button"
                            onClick={() => {
                                try {
                                    console.log('点击继续添加按钮');
                                    // 直接重置表单，不触发保存机制
                                    handleReset();
                                    // 提示用户表单已重置
                                    alert('表单已重置，可以继续添加新员工');
                                } catch (error) {
                                    console.error('重置表单失败:', error);
                                    alert('重置表单失败: ' + error.message);
                                }
                            }}
                            className="continue-button"
                        >
                            继续添加
                        </button>
                    )}
                </div>
            </form>
        </div>
    );
}

export default EmployeeForm;