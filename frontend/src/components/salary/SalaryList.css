.header-content {
    align-items: center;
    margin-top: 20px;
    padding: 0 20px;
}

.attendance-column {
    width: 100px !important;
}

.attendance-subcolumn {
    width: 50px !important;
}

.header-content h2 {
    color: #2c3e50;
    font-size: 24px;
    margin: 0;
}

.search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    gap: 20px;
}

/* 新的操作和过滤条样式 */
.action-filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 0 10px;
}

.filter-area {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-group, .department-group, .month-group {
    display: flex;
    align-items: center;
}

.search-group label, .department-group label, .month-group label {
    margin-right: 8px;
    font-weight: 500;
    color: #333;
}

/* 月份选择器样式 */
.month-picker {
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 修复日期选择器下拉菜单样式 */
.month-picker-dropdown {
    border-radius: 6px !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
    z-index: 1050 !important; /* 确保下拉菜单在最上层 */
}

/* 确保日期选择器下拉菜单内容可见 */
.ant-picker-dropdown {
    z-index: 1050 !important;
}

.ant-picker-panel-container {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
}

.month-select {
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.left-buttons, .right-buttons {
    display: flex;
    align-items: center;
    gap: 16px;
}

.management-bar h2 {
    font-size: 28px;
    color: #1f2937;
    font-weight: 600;
    margin: 0 32px;
    flex-grow: 1;
    text-align: center;
}

/* ====================== 按钮样式分组 ====================== */

/* 2. 其他按钮尺寸、背景色、悬停色 */
.back-button,
.export-button,
.print-button,
.budget-button,
.add-button,
.reset-button {
    display: inline-block;
    text-align: center;
    height: 32px;
    line-height: 32px;
    padding: 0 15px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    white-space: nowrap; /* 防止文本换行 */
    min-width: 90px; /* 最小宽度 */
    width: auto; /* 允许按钮根据内容自动调整宽度 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 返回按钮 - 蓝色 */
.back-button {
    background-color: #3b82f6;
}

.back-button:hover {
    background-color: #2563eb;
}

/* 导出按钮 - 紫色 */
.export-button {
    background-color: #8b5cf6;
}

.export-button:hover {
    background-color: #7c3aed;
}

/* 打印按钮 - 绿色 */
.print-button {
    background-color: #10b981;
}

.print-button:hover {
    background-color: #059669;
}

/* 预算按钮 - 青色 */
.budget-button {
    background-color: #06b6d4;
}

.budget-button:hover {
    background-color: #0891b2;
}

/* 添加按钮 - 橙色 */
.add-button {
    background-color: #f59e0b;
}

.add-button:hover {
    background-color: #d97706;
}

/* 重置按钮 - 红色 */
.reset-button {
    background-color: #ef4444;
}

.reset-button:hover {
    background-color: #dc2626;
}

/* 3. 所有按钮文字图标 */
.button-icon {
    font-size: 14px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
}

/* 4. page-button 尺寸、背景色、悬停色 */
.page-button {
    display: inline-block;
    text-align: center;
    height: 30px;
    line-height: 30px;
    padding: 0 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #f0f0f0; /* 浅灰色背景 */
    color: #333;
    white-space: nowrap; /* 防止文本换行 */
    min-width: 60px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-button:hover {
    background-color: #e0e0e0; /* 悬停时的颜色深一点 */
}

/* 操作按钮容器 */
.salary-action-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
}

/* 基础按钮样式 */
.salary-action-button {
    width: 45px;
    height: 26px;
    line-height: 26px;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    padding: 0;
    margin: 0;
    text-align: center;
}

/* 详情按钮 */
.salary-detail-button {
    background-color: #10b981;
}

.salary-detail-button:hover {
    background-color: #059669;
}

/* 编辑按钮 */
.salary-edit-button {
    background-color: #f59e0b;
}

.salary-edit-button:hover {
    background-color: #d97706;
}

@media print {
    .management-bar, .action-buttons, .pagination {
        display: none;
    }

    tr[data-system-title="true"] {
        display: none;
    }

    /* 统计卡片打印样式 */
    .statistics-cards {
        display: flex;
        justify-content: center;
        width: 100%;
        margin-bottom: 20px;
    }

    .statistics-cards .ant-row {
        width: 100%;
        display: flex;
        flex-wrap: nowrap;
    }

    .statistics-cards .ant-col {
        flex: 1;
        min-width: 0;
    }

    .statistics-cards .ant-card {
        width: 100%;
        min-width: 0;
        margin: 0 5px;
        transform: none !important;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        background-color: #fff !important;
    }

    .statistics-cards .ant-statistic-title {
        font-size: 12px !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .statistics-cards .ant-statistic-content-value {
        font-size: 14px !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* 响应式布局的媒体查询 */
@media screen and (max-width: 1200px) {
    .header-content {
        padding: 0 10px;
    }

    .management-bar {
        padding: 15px 0;
    }

    .management-bar h2 {
        font-size: 24px;
        margin: 0 20px;
    }



    /* 卡片响应式尺寸 */
    .ant-card {
        min-width: 160px;
    }

    th, td {
        padding: 12px 15px;
    }
}

@media screen and (max-width: 992px) {
    .search-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .management-bar {
        flex-wrap: wrap;
        gap: 10px;
    }

    .left-buttons, .right-buttons {
        gap: 10px;
    }

    .management-bar h2 {
        font-size: 20px;
        margin: 0 15px;
        flex-grow: 0;
    }


    /* 卡片响应式尺寸 - 992px */
    .ant-card {
        min-width: 130px;
    }

    .ant-statistic-title {
        font-size: 14px;
    }

    .ant-statistic-content-value {
        font-size: 20px !important;
    }
}

@media screen and (max-width: 768px) {
    .header-content h2 {
        font-size: 20px;
    }

    .management-bar h2 {
        font-size: 18px;
        margin: 0 10px;
        text-align: left;
    }

    /* 卡片响应式尺寸 - 768px */
    .ant-card {
        min-width: 110px;
    }

    .ant-card .ant-card-head-title {
        font-size: 14px;
    }

    .ant-statistic-title {
        font-size: 12px;
    }

    .ant-statistic-content-value {
        font-size: 18px !important;
    }

    th, td {
        padding: 10px;
        font-size: 13px;
    }

}

/* 统计卡片容器样式 */
.statistics-cards {
    margin: 15px 0;
    width: 100%;
}

.statistics-cards .ant-row {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
}

.statistics-cards .ant-col {
    display: flex;
    margin-bottom: 10px;
    padding: 0 5px; /* 减小列间距 */
}

/* 卡片样式 */
.ant-card {
    flex: 1;
    width: auto;
    border-radius: 8px !important; /* 减小圆角半径 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

/* 统计卡片特殊样式 */
.statistics-cards .ant-card {
    padding: 0;
}

.statistics-cards .ant-card-body {
    padding: 12px 8px !important; /* 减小内边距 */
}

/* 确保卡片内部元素也有圆角 */
.ant-card-head {
    border-radius: 8px 8px 0 0 !important;
}

.ant-card-body {
    border-radius: 0 0 8px 8px !important;
}

.ant-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 为不同卡片设置不同浅色半透明颜色 */
.ant-card:nth-child(1) {
    background-color: rgba(52, 152, 219, 0.15);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.ant-card:nth-child(2) {
    background-color: rgba(46, 204, 113, 0.15);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.ant-card:nth-child(3) {
    background-color: rgba(231, 76, 60, 0.15);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.ant-card:nth-child(4) {
    background-color: rgba(155, 89, 182, 0.15);
    border: 1px solid rgba(155, 89, 182, 0.3);
}

.ant-card:nth-child(5) {
    background-color: rgba(241, 196, 15, 0.15);
    border: 1px solid rgba(241, 196, 15, 0.3);
}

.ant-card:nth-child(6) {
    background-color: rgba(52, 73, 94, 0.15);
    border: 1px solid rgba(52, 73, 94, 0.3);
}

/* 卡片内容样式 */
.ant-card .ant-card-head {
    border-bottom: none;
    color: #333;
    background-color: transparent;
}

.ant-card .ant-card-head-title {
    color: #333;
    font-weight: 600;
}

.ant-statistic-title {
    color: #555 !important;
    font-weight: 500;
    font-size: 12px;
}

.ant-statistic-content {
    color: #333 !important;
}

.ant-statistic-content-value {
    font-size: 22px !important;
    font-weight: 700 !important;
}

/* 统计卡片中的文字大小 */
.statistics-cards .ant-statistic-title {
    font-size: 11px !important;
    margin-bottom: 2px !important;
}

.statistics-cards .ant-statistic-content-value {
    font-size: 18px !important;
}

/* 移除可能干扰Ant Design Table固定列功能的样式 */

/* 最简单直接的固定列实现 */
.table-container {
    width: 100%;
    margin-bottom: 25px;
    position: relative;
}

/* 强制覆盖Ant Design的滚动容器 */
.table-container .ant-table-container {
    overflow-x: auto !important;
}

/* 直接对表格单元格应用sticky定位 */
.table-container .ant-table-thead > tr > th:first-child,
.table-container .ant-table-tbody > tr > td:first-child {
    position: sticky !important;
    left: 0px !important;
    z-index: 100 !important;
    background: #fafafa !important;
    border-right: 2px solid #e8e8e8 !important;
}

.table-container .ant-table-thead > tr > th:nth-child(2),
.table-container .ant-table-tbody > tr > td:nth-child(2) {
    position: sticky !important;
    left: 80px !important;
    z-index: 100 !important;
    background: #fafafa !important;
    border-right: 2px solid #e8e8e8 !important;
}

/* 数据行的背景色 */
.table-container .ant-table-tbody > tr > td:first-child,
.table-container .ant-table-tbody > tr > td:nth-child(2) {
    background: #ffffff !important;
}

/* 悬停效果 */
.table-container .ant-table-tbody > tr:hover > td:first-child,
.table-container .ant-table-tbody > tr:hover > td:nth-child(2) {
    background: #f5f7fa !important;
}

/* 行跟踪器样式 */
.table-container .ant-table-tbody > tr:hover {
    background-color: #e6f7ff !important;
    box-shadow: 0 0 0 2px #1890ff !important;
    position: relative !important;
    z-index: 1 !important;
}

.table-container .ant-table-tbody > tr:hover > td {
    background-color: #e6f7ff !important;
    border-color: #1890ff !important;
}

/* 行跟踪提示器样式 */
.row-tracker-tooltip {
    animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 移除信息栏相关样式 */

/* 强制实现固定列效果 */
.table-container .ant-table-thead > tr > th:nth-child(1),
.table-container .ant-table-tbody > tr > td:nth-child(1) {
    position: sticky !important;
    left: 0 !important;
    z-index: 10 !important;
    background: #fafafa !important;
    border-right: 1px solid #f0f0f0 !important;
}

.table-container .ant-table-thead > tr > th:nth-child(2),
.table-container .ant-table-tbody > tr > td:nth-child(2) {
    position: sticky !important;
    left: 80px !important;
    z-index: 10 !important;
    background: #fafafa !important;
    border-right: 1px solid #f0f0f0 !important;
}

.table-container .ant-table-tbody > tr > td:nth-child(1),
.table-container .ant-table-tbody > tr > td:nth-child(2) {
    background: #fff !important;
}

.table-container .ant-table-tbody > tr:hover > td:nth-child(1),
.table-container .ant-table-tbody > tr:hover > td:nth-child(2) {
    background: #f5f7fa !important;
}

/* 移除可能干扰Ant Design Table的通用样式 */

/* 分页样式 */
.ant-pagination {
    display: flex !important;
    align-items: center !important;
    justify-content: center;
    gap: 8px;
    height: 32px;
    line-height: 32px;
}

.page-button.page-info {
    background-color: #f0f0f0;
    color: #333;
    cursor: default;
    min-width: 120px;
    font-size: 12px;
    box-shadow: none;
    border: 1px solid #e0e0e0;
}

.ant-pagination-options {
    display: flex !important;
    align-items: center !important;
    height: 32px !important;
}

.ant-select-selector {
    height: 32px !important;
}

.ant-pagination-options-quick-jumper input {
    height: 32px;
    line-height: 32px;
}

.filter-area {
    position: relative;
    z-index: 1000;
}

/* 部门选择器样式优化 */
.salary-department-select {
    width: 100%;
}

.salary-department-select .ant-select-selector {
    height: 32px !important;
    padding: 0 11px !important;
    border: 2px solid #e1e8ed !important;
    border-radius: 6px !important;
    box-shadow: none !important;
    background-color: white !important;
}

.salary-department-select.ant-select-focused .ant-select-selector,
.salary-department-select .ant-select-selector:hover,
.salary-department-select.ant-select-open .ant-select-selector {
    border-color: #2563eb !important;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2) !important;
}

/* 薪资列表特定的搜索框样式 */
.salary-search-input {
    height: 32px !important;
}

.salary-search-input .ant-input-wrapper {
    height: 32px !important;
}

.salary-search-input .ant-input {
    height: 32px !important;
    line-height: 32px !important;
    border: 2px solid #e1e8ed !important;
    border-radius: 6px 0 0 6px !important;
    box-sizing: border-box !important;
}

.salary-search-input .ant-input-group-addon {
    height: 32px !important;
}

.salary-search-input .ant-input-search-button {
    height: 32px !important;
    border-radius: 0 6px 6px 0 !important;
}

/* 修复搜索框错位问题 */
.salary-search-input.ant-input-affix-wrapper {
    padding: 0 11px !important;
    border: 2px solid #e1e8ed !important;
    border-radius: 6px !important;
    height: 32px !important;
    line-height: 32px !important;
    box-sizing: border-box !important;
}

.salary-search-input.ant-input-affix-wrapper .ant-input {
    border: none !important;
    height: 28px !important;
    line-height: 28px !important;
    padding: 0 !important;
}

.salary-search-input.ant-input-affix-wrapper .ant-input-suffix {
    margin: 0 !important;
}

.department-select .ant-select-selection-item {
    line-height: 30px !important;
    font-size: 14px;
}

.department-select + .ant-select-dropdown {
    border-radius: 6px !important;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
    max-height: 400px !important;
    overflow-y: auto !important;
    margin-top: -120px !important; /* 向上偏移，留出更多空间 */
}

/* 修复下拉菜单双滚动条问题 */
.ant-select-dropdown .rc-virtual-list {
    max-height: 280px !important;
    overflow-y: hidden !important; /* 隐藏内层滚动条 */
}

.ant-select-dropdown .rc-virtual-list-holder {
    overflow-y: hidden !important; /* 隐藏内层滚动条 */
    overflow-anchor: none !important;
}

.ant-select-dropdown .rc-virtual-list-holder-inner {
    position: relative !important;
}

/* 确保外层容器有滚动条 */
.ant-select-dropdown {
    overflow-y: auto !important;
    max-height: 300px !important;
}

.department-select + .ant-select-dropdown .ant-select-item {
    padding: 8px 12px !important;
    font-size: 14px;
}

.department-select + .ant-select-dropdown .ant-select-item-option-selected {
    background-color: #e6f7ff !important;
    font-weight: 600;
}

.department-select + .ant-select-dropdown .ant-select-item-option-active {
    background-color: #f5f5f5 !important;
}